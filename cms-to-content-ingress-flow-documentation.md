# CMS Listener to Content Ingress v1 to DynamoDB Flow Documentation

## Overview

This document describes the complete data flow from CMS systems (Umbraco/Contentful) through the CMS Listener Service to Content Ingress v1, ultimately storing data in DynamoDB and how that data is consumed by brand websites and aggregator websites.

## Architecture Components

### 1. CMS Sources
- **Umbraco CMS**: Primary content management system for ATG brands
- **Contentful CMS**: Legacy content management system (still supported)

### 2. CMS Listener Service
The CMS Listener Service acts as the webhook receiver and event publisher for content changes.

#### Key Handlers:
- **Content Handler** (`/content`): General page content
- **Production Handler** (`/production`): Show/event content
- **Brand Handler** (`/brand`): Brand configuration
- **Venue Config Handler** (`/venue-config`): Venue settings
- **Redirect Handler** (`/redirect`): URL redirects
- **Inventory Handler** (`/inventory`): Inventory system configuration

#### Event Processing Flow:
1. **Webhook Reception**: API Gateway receives webhooks from CMS
2. **Content Transformation**: Raw CMS data is mapped to standardized format
3. **Event Publishing**: Transformed data is published to EventBridge

### 3. EventBridge Integration
Events are published to the Content Event Bus with specific patterns:

```
Source: atg.cms-listener.{cms}.{content-type}
Detail-Type: publish | delete | unpublish
```

Example sources:
- `atg.cms-listener.umbraco.content`
- `atg.cms-listener.umbraco.production`
- `atg.cms-listener.umbraco.brand`

### 4. Content Ingress v1 Service
Consumes events from EventBridge and processes them through Step Functions.

#### Step Function Workflow:
1. **Event Type Check**: Determines processing path based on event type
2. **Content Processing**: 
   - **Publish/Update**: Fetches content from CMS API and saves to DynamoDB
   - **Delete/Unpublish**: Removes content from DynamoDB
   - **Redirects**: Updates KV store for URL redirects

#### Key Operations:
- **Fetch from CMS API**: Retrieves full content data using CMS APIs
- **Data Transformation**: Converts CMS data to `UmbracoItem` model
- **DynamoDB Storage**: Saves transformed data with proper keys
- **Cache Invalidation**: Triggers Next.js revalidation when URLs change

## DynamoDB Data Structure

### Content Table Schema
Brand-scoped DynamoDB table with the following key patterns:

#### Primary Keys:
- **Content Pages**: `PK=CONTENT`, `SK=/page-url/`
- **Site Settings**: `PK=SETTINGS`, `SK=/`
- **Tour Content**: `PK=TOUR`, `SK=websiteSlug`

#### Global Secondary Index (GSI1):
- **GSI1PK**: Content type grouping
- **GSI1SK**: Content ID for lookups

### Data Examples:

#### Content Page Item:
```json
{
  "PK": "CONTENT",
  "SK": "/events/the-queen-of-versailles/",
  "GSI1PK": "CONTENT",
  "GSI1SK": "cc33b25b-245b-4a34-b1c6-6a99b1671ac9",
  "_id": "cc33b25b-245b-4a34-b1c6-6a99b1671ac9",
  "contentTypeAlias": "production",
  "name": "The Queen of Versailles",
  "blocks": [...],
  "metadata": {...},
  "showSlug": "the-queen-of-versailles",
  "venueConfig": {...}
}
```

#### Settings Item:
```json
{
  "PK": "SETTINGS",
  "SK": "/",
  "GSI1PK": "SETTINGS",
  "GSI1SK": "brand-config-id",
  "brandConfig": {...},
  "siteSettings": {...}
}
```

## Website Data Consumption

### Brand Websites (Single/Multi-venue sites)

#### Server-Side Rendering (SSR) Data Sources:
1. **DynamoDB Content**: Page content, blocks, and metadata
   - Uses `getPageContent()` function
   - Cached with Next.js `unstable_cache`
   - Revalidation: 120 seconds

2. **Typesense**: Show/venue/performance discovery data
   - Fast search and filtering
   - Aggregated performance data
   - Venue location information

3. **Bolt GraphQL APIs**: Real-time performance data
   - Calendar dates service
   - Catalogue service
   - Pricing and availability

#### Data Flow for Brand Websites:
```
DynamoDB (Page Content) → Next.js SSR → Page Rendering
Typesense (Show Data) → Client/SSR → Show Context
Bolt GraphQL (Performances) → Apollo Client → Calendar Views
```

### Aggregator Websites (Brand-level discovery)

#### Server-Side Rendering (SSR) Data Sources:
1. **DynamoDB Content**: Editorial content blocks and page metadata
   - Same `getPageContent()` pattern as brand sites
   - Aggregator-specific page templates

2. **Typesense**: Cross-venue show discovery
   - `getTypesenseShowContent()` for show details
   - `getUpcomingShowPerformances()` for listings
   - Genre and venue filtering

3. **Bolt GraphQL APIs**: Performance calendars and pricing
   - Cross-venue performance data
   - Real-time availability

#### Data Flow for Aggregator Websites:
```
DynamoDB (Editorial Content) → Next.js SSR → Page Structure
Typesense (Show Discovery) → SSR/Client → Show Listings
Bolt GraphQL (Performances) → Apollo Client → Calendar Data
```

## Key Features

### 1. Real-time Content Updates
- Webhook-driven updates from CMS
- Immediate EventBridge publishing
- Step Function processing within seconds
- Next.js cache invalidation for changed URLs

### 2. URL Management
- Automatic redirect creation for URL changes
- KV store synchronization for redirects
- Child content URL updates for hierarchical changes

### 3. Brand Isolation
- Brand-scoped DynamoDB tables
- EventBridge filtering by brand domain
- Isolated content processing per brand

### 4. Error Handling
- Step Function retry mechanisms
- Slack notifications for failures
- Dead letter queues for failed events

### 5. Cache Strategy
- Next.js SSR caching (120s revalidation)
- On-demand revalidation for content changes
- Typesense for fast discovery queries
- GraphQL for real-time performance data

## Performance Considerations

### DynamoDB Access Patterns:
- **Read**: Single-item gets by page URL (hot path)
- **Write**: Single-item puts for content updates
- **GSI**: Lookups by content ID for updates

### Caching Strategy:
- **SSR Cache**: 120-second revalidation for page content
- **CDN Cache**: CloudFront for static assets
- **API Cache**: Typesense for aggregated data
- **Real-time**: GraphQL for live performance data

### Scalability:
- **Serverless**: Auto-scaling Lambda functions
- **Event-driven**: Asynchronous processing
- **Brand isolation**: Independent scaling per brand
- **Global distribution**: Multi-region DynamoDB replication

## Monitoring and Observability

### Metrics:
- EventBridge event counts and failures
- Step Function execution metrics
- DynamoDB read/write capacity
- Next.js cache hit rates

### Logging:
- Structured logging throughout the pipeline
- Content transformation tracking
- Error correlation across services

### Alerting:
- Slack notifications for processing failures
- CloudWatch alarms for service health
- Step Function failure notifications

## Related Services

### Catalogue Aggregator Service (CAS)
- Consumes same CMS events in parallel
- Aggregates content for Typesense indexing
- Provides search and discovery capabilities

### Calendar Dates Service
- Provides performance calendar data
- GraphQL API for real-time queries
- Bolt team managed service

### Catalogue Service
- Brand and event metadata
- GraphQL API for show context
- Performance relationship data

## Detailed Code Examples

### CMS Listener Event Publishing

<augment_code_snippet path="cms-listener-service/packages/cms-listener-service/src/handlers/umbraco/content/index.ts" mode="EXCERPT">
```typescript
export const handler = baseHandler(
  __filename,
  async (event, logger, shouldReturnLegacyEvent) => {
    const body = JSON.parse(event.body!) as Content & {
      locale: UmbracoLocale
    }
    const siteBuilderData = mapUmbracoBodyOld(body, logger)
    await raiseContentEvent(siteBuilderData, 'content', 'new-or-updated')

    const cmsEvent = mapUmbracoBody(body, logger)
    await raiseContentEventNew(cmsEvent, 'umbraco', 'content', 'publish')
    // ...
  }
)
```
</augment_code_snippet>

### Content Ingress Step Function

<augment_code_snippet path="catalogue-sites/packages/content-ingress/resources/step-function.ts" mode="EXCERPT">
```typescript
export const processUmbracoEvents: StateMachine = {
  name: '${self:custom.resourcePrefix}-process-umbraco-events--${self:custom.stage}',
  definition: {
    StartAt: 'AddLogger',
    States: {
      CheckEventType: {
        Type: 'Choice',
        Choices: [
          {
            Or: [
              { Variable: '$.detail-type', StringEquals: 'delete' },
              { Variable: '$.detail-type', StringEquals: 'unpublish' }
            ],
            Next: 'DeleteContent',
          },
        ],
        Default: 'PublishContent',
      },
      // ...
    }
  }
}
```
</augment_code_snippet>

### DynamoDB Data Access

<augment_code_snippet path="catalogue-sites/packages/ui/data/get-page-content.ts" mode="EXCERPT">
```typescript
export const getPageContent = unstable_cache(
  async (pageUrl: string) => {
    const data = await dynamoDbClient.send(
      new GetItemCommand({
        TableName: process.env.TABLE_NAME,
        Key: {
          PK: { S: 'CONTENT' },
          SK: { S: pageUrl },
        },
      })
    )
    if (data.Item) {
      const content = unmarshall(data.Item)
      return { ...content, pageUrl } as Record<string, any>
    }
    // ...
  },
  ['page-content'],
  { tags: ['page'], revalidate: 120 }
)
```
</augment_code_snippet>

## Event Flow Patterns

### EventBridge Event Structure
```json
{
  "source": "atg.cms-listener.umbraco.production",
  "detail-type": "publish",
  "detail": {
    "_id": "cc33b25b-245b-4a34-b1c6-6a99b1671ac9",
    "brand": "emersoncolonialtheatre.com",
    "contentTypeAlias": "production",
    "pageUrl": "/events/the-queen-of-versailles/",
    "showSlug": "the-queen-of-versailles",
    "blocks": [...],
    "metadata": {...}
  }
}
```

### Content Ingress Processing
1. **Event Reception**: EventBridge triggers Step Function
2. **Content Fetching**: Retrieve full content from Umbraco API
3. **Data Transformation**: Convert to UmbracoItem model
4. **DynamoDB Storage**: Save with proper partition/sort keys
5. **Cache Invalidation**: Trigger Next.js revalidation if URL changed

## Troubleshooting Guide

### Common Issues

#### 1. Content Not Appearing on Website
- **Check**: EventBridge event delivery
- **Verify**: Step Function execution status
- **Confirm**: DynamoDB item exists with correct keys
- **Test**: Next.js cache revalidation

#### 2. Stale Content After Updates
- **Check**: Cache invalidation triggered
- **Verify**: Next.js revalidation API response
- **Confirm**: DynamoDB item updated timestamp
- **Clear**: Browser/CDN cache if needed

#### 3. Step Function Failures
- **Review**: CloudWatch logs for error details
- **Check**: Umbraco API connectivity
- **Verify**: DynamoDB write permissions
- **Monitor**: Slack failure notifications

### Debugging Commands

#### Check EventBridge Events
```bash
aws events list-rules --event-bus-name com.atgtickets.production.bus.content
```

#### Query DynamoDB Content
```bash
aws dynamodb get-item \
  --table-name csite-brandname--production \
  --key '{"PK":{"S":"CONTENT"},"SK":{"S":"/page-url/"}}'
```

#### Monitor Step Function Executions
```bash
aws stepfunctions list-executions \
  --state-machine-arn arn:aws:states:region:account:stateMachine:name
```

## Security Considerations

### Authentication & Authorization
- **CMS Webhooks**: HMAC signature validation
- **EventBridge**: IAM role-based access
- **DynamoDB**: Least privilege IAM policies
- **API Access**: Brand-scoped resource isolation

### Data Protection
- **Encryption**: DynamoDB encryption at rest
- **Transit**: TLS for all API communications
- **Access Logs**: CloudTrail for audit trails
- **Secrets**: AWS Secrets Manager for API keys

## Future Enhancements

### Planned Improvements
1. **Content Versioning**: Track content history in DynamoDB
2. **Preview Mode**: Support for draft content preview
3. **Multi-region**: Cross-region content replication
4. **Performance**: Enhanced caching strategies
5. **Monitoring**: Advanced observability metrics

### Migration Considerations
- **Content Ingress v2**: New service with enhanced features
- **Backward Compatibility**: Gradual migration strategy
- **Data Migration**: Automated content transfer tools
- **Testing**: Comprehensive validation procedures
