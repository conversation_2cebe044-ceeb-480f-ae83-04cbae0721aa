import type { Content } from '@umbraco/headless-client'
import type {
  Block,
  HRef,
  UmbracoImage,
  UmbracoLink,
  UmbracoLocale,
  UpsellList,
} from './generics'

export interface OnSaleDate {
  promoCode: string
  onSaleDate: string
  soldOut: boolean
  offSaleDate?: string
  onSaleType: string
  description: string
  contentTypeAlias: string
  hideDateAndTime: boolean
}

export interface Urls {
  'en-us': string
}

export interface VenueConfig {
  _id: string
  _url: string
  name: string
  _urls: Urls
  _level: number
  _links: {
    seatmap: HRef
  }
  parentId: string
  sortOrder: number
  seatMap: null
  venueSlug: string
  _createDate: string
  _updateDate: string
  _writerName: string
  inventoryID: string
  _creatorName: string
  _hasChildren: boolean
  contentfulVenueId: string
  venueConfigName: string
  contentTypeAlias: string
}

export interface InformationBar {
  informationBar: string
  contentTypeAlias: string
}

export interface SocialMediaTag {
  image?: null
  title: string
  description: string
  socialNetwork: string
  contentTypeAlias: string
}

export interface ShowPageUrlOverride {
  url: string
  name: string
  type: string
}

export interface BuyTicketsUrlOverride {
  url: string
  name: string
  type: string
}

export interface Entity {
  content: {
    _links: {
      performanceentity: {
        href: string
        title: string
      }
    }
    contentTypeAlias: 'performanceEntity'
    performanceEntity: {
      contentTypeAlias: 'eventEntity'
      _id: string
      name: string
      entityURL: UmbracoLink
      entityName: string
      entityTypeSlug:
        | 'comedian'
        | 'concert_tour'
        | 'magician'
        | 'online_personality'
        | 'performer_musician'
        | 'performer_sports_player'
        | 'show'
        | 'sports_team'
        | 'sports_tournament'

      entityRelevantURLs: UmbracoLink[]
      entityMusicbrainzID: string
    } | null
    performanceEntityRole: 'headliner' | 'supporter'
  }
}

export interface ProductionContent extends Content {
  locale?: UmbracoLocale //'en-US',
  genreList: string[]
  owner: string | null | number
  venue: string
  blocks: Block[]
  entities: Entity[]
  noIndex: boolean
  showSlug: string
  websiteSlug?: string
  inventorySlug?: string
  doorsOpen: string
  eventName: string
  intervals: number
  isSoldOut: boolean
  pageName: string
  startTime: string
  upsellList: UpsellList[]
  ctaOverride: string
  embargoDate: string
  externalURL: string
  onSaleDates: OnSaleDate[]
  runningTime: string
  salesPeriod: string
  venueConfig: VenueConfig | null
  browserTitle: string
  eventSubName: string
  eventDescription: string
  hideInfoBars: boolean
  hideFromBreadcrumbs: boolean
  hideFromSearchEngines?: boolean
  additionalCTA: string
  showCardImage: UmbracoImage | null
  showPageLabel: string
  forceFreeEvent: boolean
  specialEventType?: string
  lastReviewDate: string
  nextReviewDate: string
  productionShot: UmbracoImage | null
  productionShotTall: UmbracoImage | null
  seoDescription: string
  buyTicketsLabel: string
  informationBars: InformationBar[]
  overrideQueueID: string
  overrideUpsells: boolean
  socialMediaTags?: SocialMediaTag[]
  additionalCTAURL: UmbracoLink | null
  groupInformation: string
  performanceTimes: string
  showPromoCodeBox: boolean
  useSpecificQueue: boolean
  accessPerformances: string[]
  pricingDescription: string
  showPageUrlOverride: UmbracoLink | null
  warningsAndGuidance: string
  buyTicketsUrlOverride: BuyTicketsUrlOverride | null
  contentManagementNotes: string
  mobileShowCardOverride?: null
  forcePostponedOrCanceled: string
  mobileProductionShotOverride?: null
  checkoutCalendarVerticalImage: UmbracoImage | null
  checkoutCalendarHorizontalImage: UmbracoImage | null
  firstPerformanceDate?: string
  twitterImage: UmbracoImage | null
  twitterTitle: string
  twitterDescription: string
  openGraphImage: UmbracoImage | null
  openGraphTitle: string
  openGraphDescription: string
  resellAvailable: boolean
  resellURL?: string
  resellInformation?: string
  thirdPartyEvent?: boolean
}
