import {
  type BaseObject,
  type Production as ContentfulProduction,
  type Upsell as ContentfulUpsell,
  type Tour as ContentfulTour,
  type Upsell,
  type VenueConfig as ContentfulVenueConfig,
  type Venue as ContentfulVenue,
} from '@atg-digital/contentful-types'
import { documentToHtmlString } from '@contentful/rich-text-html-renderer'

import { convertAssetToImage, flattenFields, removeKeys } from './common'

import type {
  ImageInfo,
  Production as CmsProduction,
  Tour as CmsTour,
  UpsellItem,
  Venue,
} from '@atg-digital/cms-types'
import type { Entry, EntrySkeletonType } from 'contentful'

const flattenContentfulData = <T>(
  data: unknown,
  keysToFlatten = ['fields', 'sys', 'en-US']
) =>
  flattenFields(
    removeKeys(data, new Set(['metadata', 'environment', 'revision', 'space'])),
    keysToFlatten
  ) as T

const convertMinutesToString = (mins: number): string => {
  const hours = Math.floor(mins / 60)
  const minutes = mins % 60

  const parts: string[] = []

  if (hours > 0) {
    parts.push(hours === 1 ? '1 hour' : `${hours} hours`)
  }

  if (minutes > 0) {
    parts.push(minutes === 1 ? '1 minute' : `${minutes} minutes`)
  }

  return parts.join(' and ') || '0 minutes'
}

const formatRunningTime = (duration?: number, interval?: boolean) => {
  if (!duration) return undefined

  const formatDuration = convertMinutesToString(duration)
  const formatInterval = interval ? 'incl. interval' : ''

  return `${formatDuration} ${formatInterval}`
}

const mapUpsell = (upsell: ContentfulUpsell): UpsellItem => ({
  internalName: upsell.name,
  contentTypeAlias: 'upsell',
  image: upsell.imageUrl ? { src: upsell.imageUrl } : undefined,
  inventoryId: upsell.avId,
  contentfulId: upsell.id,
  price: upsell.price,
  title: upsell.title,
  unitTypeLabel: upsell.unitType,
  type: upsell.type,
  subType: upsell.loungeSubtype,
  description: upsell.info,
  moreInfo: upsell.moreInfo ? documentToHtmlString(upsell.moreInfo) : undefined,
  standaloneDescription: upsell.standaloneDescription
    ? documentToHtmlString(upsell.standaloneDescription)
    : undefined,
  selectorType: upsell.selectorType,
})

export const mapContentfulBody = (data: unknown) => {
  const payload = flattenContentfulData<BaseObject>(data)

  switch (payload.contentType?.id) {
    case 'tourCard':
      return mapContentfulTour(
        payload as ContentfulTour & {
          brand: string
          brandAlias: string
        }
      )
    case 'production':
      return mapContentfulProduction(
        payload as ContentfulProduction & {
          brand: string
          brandAlias: string
        }
      )

    case 'venueConfig':
      return mapContentfulVenue(payload as ContentfulVenueConfig)

    case 'venue':
      return mapContentfulVenue(payload as ContentfulVenue)
    default:
      throw new Error(`Unknown content type: ${payload.contentType?.id}`)
  }
}

export const mapContentfulProduction = (
  payload: ContentfulProduction & {
    brand: string
    brandAlias: string
  }
): CmsProduction => {
  const description = payload.overrideDescription ?? payload.title.description

  const productionShot = convertAssetToImage(
    payload.overrideHeaderAssets?.[0] ?? payload.title.headerAssets?.[0]
  )

  const showCardImage =
    convertAssetToImage(
      payload.overrideBodyAssets?.[0] ?? payload.title.bodyAssets?.[0]
    ) ?? productionShot

  const mapped: CmsProduction = {
    _id: payload.id,
    brandAlias: payload.brandAlias,
    blocks: [],
    brand: payload.brand,
    contentTypeAlias: 'production',
    showSlug: payload.title.titleSlug?.toLowerCase(),
    websiteSlug: payload.title.titleSlug?.toLowerCase(),
    inventorySlug: payload.title.titleSlug?.toLowerCase(),
    priceMin: payload.priceMinimum ?? 0,
    transactionFee: payload.priceTransactionFee ?? 0,
    venue: mapContentfulVenue(payload.venue),
    pageUrl: `/shows/${payload.title.titleSlug}/${payload.venue.venueSlug}/`,
    name: payload.productionName?.trim(),
    details: {
      title: payload.title.topLevelTitle.title!.trim(),
      genres:
        payload.title.topLevelTitle.genreList?.map(
          (genre) => genre?.genreName ?? ''
        ) ?? [],
      description: description ? documentToHtmlString(description) : undefined,
      overview: payload.overrideOverview ?? payload.title.overview,
      reviews: payload.title.topLevelTitle.reviews?.map((review) => ({
        content: review.content ?? '',
        author: review.quoteAuthor,
        score: review.quoteScore,
      })),
      quotes: payload.title.topLevelTitle.quotes?.map((quote) => ({
        content: quote.content,
        author: quote.quoteAuthor,
      })),
      casting: payload.casting?.map((cast) => ({
        characterTitle: cast.characterTitle,
        characterName: cast.characterName,
        actor: {
          name: cast.actor.actorName,
          avatarUrl: cast.actor.avatarUrl,
        },
        schedule: cast.actorSchedule,
      })),
      salesPeriod: payload.salePeriod,
      accessPerformances: [],
      doorsOpen: payload.doorsTimes,
      performanceTimes: payload.showTimes?.join(', '),
      externalURL: payload.externalBuyTicketsLink,
      isSoldOut: Boolean(payload.soldOut),
      resellAvailable: false,
      intervals:
        payload.title.interval === undefined
          ? undefined
          : Number(payload.title.interval),
      runningTime: formatRunningTime(
        payload.title.duration,
        payload.title.interval
      ),
    },
    metadata: {
      createdBy: payload.createdBy?.id,
      createdAt: payload.createdAt,
      updatedBy: payload.updatedBy?.id,
      updatedAt: payload.updatedAt,
      browserTitle: payload.browserTitle,
      seoDescription: payload.title.seoDescription,
      socialMetaData: [],
      entities:
        payload.entities?.map((entity) => ({
          contentTypeAlias: 'performanceEntity',
          performanceEntity: {
            id: entity.performanceEntity.id,
            name: entity.performanceEntity.entityName ?? '',
            url: entity.performanceEntity.entityUrl
              ? {
                  url: entity.performanceEntity.entityUrl,
                }
              : undefined,
            contentTypeAlias: 'eventEntity',
            type: entity.performanceEntity.entityTypeSlug,
            relevantURLs: entity.performanceEntity.entityRelevantURLs?.map(
              (url) => ({
                url,
              })
            ),
            musicbrainzId: entity.performanceEntity.entityMusicbrainzId,
          },
          performanceEntityRole: entity.performanceEntityRole,
        })) ?? [],
    },
    media: {
      productionShot,
      showCardImage,
      trailerUrl:
        payload.overrideTrailerUrl ?? payload.title.topLevelTitle.trailerUrl,
      checkoutCalendarHorizontalImage: payload.title.imageOverrideHorizontal
        ? {
            src: payload.title.imageOverrideHorizontal,
          }
        : undefined,
      checkoutCalendarVerticalImage: payload.title.imageOverrideVertical
        ? {
            src: payload.title.imageOverrideVertical,
          }
        : undefined,
    },
    upsellList: ((payload.upsells ?? []).length > 0
      ? (payload.upsells ?? []).map(mapUpsell) // Use Production level upsells by default.
      : // Use Venue level upsells as a fallback if there are no Production level upsells.
        (payload.venue.venue.upsells ?? []).map(mapUpsell)
    )
      // Filter out any upsells that have type 'Link'.
      // There is a case when an upsell is attached to a production, but that upsell is not published yet.
      // In this case, an upsell object does not have any upsell fields and has type fields equals to 'Link'.
      // We need to filter out these upsells because they are basically empty objects without any upsell fields.
      .filter((upsell) => (upsell.type as string) !== 'Link'),
    locale: 'en-GB',
    settings: {
      brandConfig: {
        availableLanguages: ['en-GB'],
      },
      hide: {
        searchEngines: Boolean(payload.noindex),
      },
      overrides: {
        upsells:
          (payload.upsells ?? []).length > 0 ||
          (payload.venue.venue.upsells ?? []).length > 0,
        skipUpsells: Boolean(payload.skipUpsell),
        buyTicketsUrl: payload.externalBuyTicketsLink?.trim(),
        queueId: payload.queueitEventId,
        skipTicketProtection: Boolean(payload.skipTicketProtection),
        showPageUrl: {
          url: payload.tourUrl,
        },
      },
      noIndex: Boolean(payload.noindex),
      showPromoCodeBox: Boolean(payload.manualPromoCodes),
      informationBars: [],
      useSpecificQueue: Boolean(payload.queueitEventId),
    },
    ctas: {},
    pageSlug: payload.title.titleSlug,
    thirdPartyEvent: Boolean(payload.thirdPartyEvent),
  }

  return mapped
}

export const mapContentfulTour = (
  payload: ContentfulTour & {
    brand: string
    brandAlias: string
  }
): CmsTour => {
  if (!payload.title?.titleSlug) {
    throw new Error(`Tour ${payload.id} has no title`)
  }
  if (!payload.production?.length) {
    throw new Error(`Tour ${payload.title.titleSlug} has no productions`)
  }
  if (
    !payload.isSeason &&
    !payload.production.every(
      (production) => production.title.titleSlug === payload.title.titleSlug
    )
  ) {
    throw new Error(
      `Tour ${payload.title.titleSlug} has productions with different titles`
    )
  }
  const productions = Array.from(
    new Map(
      payload.production.map((p) => [
        `${p.title.titleSlug}|${p.venue.venueSlug}`,
        {
          showSlug: p.title.titleSlug,
          venueSlug: p.venue.venueSlug,
          brandAlias: payload.brandAlias,
          id: p.id,
        },
      ])
    ).values()
  )

  const tourShot = convertAssetToImage(payload.headerAsset)

  const tourCardImage = convertAssetToImage(payload.bodyAsset) ?? tourShot

  const mapped: CmsTour = {
    _id: payload.id,
    brandAlias: payload.brandAlias,
    blocks: [],
    brand: payload.brand,
    contentTypeAlias: 'tour',
    showSlug: payload.title.titleSlug?.toLowerCase(),
    pageUrl: `/shows/${payload.title.titleSlug}/`,
    name: payload.tourName!.trim(),
    details: {
      title: payload.title.topLevelTitle.title!.trim(),
      genres:
        payload.title.topLevelTitle.genreList?.map(
          (genre) => genre?.genreName ?? ''
        ) ?? [],
      subTitle: payload.title.title!.trim(),
      salesPeriod: payload.salePeriod,
      runningTime: formatRunningTime(
        payload.title.duration,
        payload.title.interval
      ),
      intervals:
        payload.title.interval === undefined
          ? undefined
          : Number(payload.title.interval),
    },
    metadata: {
      createdBy: payload.createdBy?.id,
      createdAt: payload.createdAt,
      updatedBy: payload.updatedBy?.id,
      updatedAt: payload.updatedAt,
      seoDescription: payload.title.seoDescription,
    },
    media: {
      tourShot,
      tourCardImage,
      trailerUrl: payload.title.topLevelTitle.trailerUrl,
    },
    locale: 'en-GB',
    productions,
    isTour: payload.isTour,
    isSeason: payload.isSeason,
  }

  return mapped
}

export const mapContentfulUpsell = (
  data: unknown,
  productions: Entry<EntrySkeletonType, undefined, string>[]
): UpsellItem => {
  const mappedProductions = productions
    // Flatten CMS Production fields
    .map((production) =>
      flattenContentfulData<ContentfulProduction>(production)
    )
    // Filter out productions that have skipUpsell flag set to true
    .filter((production) => !production.skipUpsell)
    // Group productions by venueSlug and map to showSlugs
    .reduce(
      (
        mapped: { venueSlug: string; showSlugs: string[] }[],
        { venue, title }
      ) => {
        const found = mapped.find(
          ({ venueSlug }) => venueSlug === venue.venueSlug
        )
        if (found) {
          found.showSlugs.push(title.titleSlug)
        } else {
          mapped.push({
            venueSlug: venue.venueSlug,
            showSlugs: [title.titleSlug],
          })
        }
        return mapped
      },
      []
    )

  const upsell: Upsell & { sys: { id: string } } = flattenContentfulData(data, [
    'fields',
    'en-US',
  ])

  return {
    ...mapUpsell(upsell),
    contentfulId: upsell.sys.id,
    venues: mappedProductions.map(({ venueSlug, showSlugs }) => ({
      venueSlug,
      showSlugs: [...new Set(showSlugs)],
    })),
  }
}

interface WithAssets {
  assets?: {
    url_1280x720?: string
    url_413x232?: string
    altDescription?: string
  }[]
}

const buildGallery = (assets?: WithAssets['assets']) =>
  (assets ?? []).flatMap(
    ({ url_1280x720, url_413x232, altDescription }) =>
      [
        url_1280x720 && {
          src: url_1280x720,
          width: 1280,
          height: 720,
          alt: altDescription,
        },
        url_413x232 && {
          src: url_413x232,
          width: 413,
          height: 232,
          alt: altDescription,
        },
      ].filter(Boolean) as ImageInfo[]
  )

/** Discriminates the two payload shapes at runtime */
const isConfig = (
  payload: ContentfulVenueConfig | ContentfulVenue
): payload is ContentfulVenueConfig => 'venue' in payload // venue ⟺ config

export function mapContentfulVenue(
  payload: ContentfulVenueConfig | ContentfulVenue
): Venue {
  // whichever object actually holds the “venue” fields
  const v = isConfig(payload) ? payload.venue : payload

  /* ---------- shared helpers ---------- */
  const gallery = buildGallery(isConfig(payload) ? v.assets : payload.assets)
  const about = v.about ? documentToHtmlString(v.about) : undefined

  /* ---------- build the Venue ---------- */
  return {
    /* ───── core fields ───── */
    id: v.id,
    name: v.venueName!,
    contentTypeAlias: 'venue',
    slug: v.venueSlug,
    address: v.address,
    streetAddress: v.streetAddress,
    city: v.city?.trim(),
    postalCode: v.postalCode,
    country: v.country,
    state: v.state,
    region: v.venueRegion,
    timeZoneLocation: v.timeZone ?? 'Europe/London',
    isATGVenue: v.isATGVenue ?? true,
    upsellTypes: v.hasUpsellTypes ?? [],

    /* ───── presentation layer ───── */
    details: {
      gallery,
      about,
      map: {
        location: {
          lat: v.addressCoordinates?.lat ?? 0,
          lon: v.addressCoordinates?.lon ?? 0,
        },
        snapshotUrl: v.mapSnapshot,
        googleMapsUrl: v.googleMapsUrl,
      },
      transportOptions: {
        bus: v.transportBus,
        parking: v.transportParking,
        train: v.transportTrain,
        closestStation: v.closestTubeStation,
        stationType: v.stationType,
      },
    },

    /* ───── operational / box-office config ───── */
    config: isConfig(payload)
      ? {
          configName: payload.venueConfigName!,
          venueConfigSlug: payload.venueSlug,
          inventoryId: payload.avVenueId,
          boxOfficeHours: v.boxOfficeHours,
          boxOfficeNotes: v.boxOfficeNotes,
          amenities: v.amenities ?? [],
          isThirdParty: payload.isThirdPartyVenue ?? false,
          hideMembershipPromotion: payload.hideMembershipPromotion ?? false,
        }
      : undefined,
  }
}
