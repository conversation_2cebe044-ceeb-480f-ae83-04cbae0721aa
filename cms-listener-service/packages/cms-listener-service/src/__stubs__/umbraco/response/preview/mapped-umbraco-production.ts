import type { Production } from '@atg-digital/cms-types'

const BRAND = 'emersoncolonialtheatre.com'
const VENUE_SLUG = 'emerson-colonial-theatre'
const SHOW_SLUG = 'the-queen-of-versailles'

export const mappedUmbracoProduction: Production = {
  pathDomain: 'emersoncolonialtheatre-com',
  brandAlias: 'emcol',
  brand: BRAND,
  name: 'The Queen of Versailles',
  pageUrl: '/emersoncolonialtheatre-com/events/the-queen-of-versailles/',
  _id: 'cc33b25b-245b-4a34-b1c6-6a99b1671ac9',
  locale: 'en-US',
  contentTypeAlias: 'production',
  blocks: [
    {
      richTextContent:
        '<h2 style="text-align: left;">GENERAL PUBLIC ON SALE BEGINS WEDNESDAY, FEBRUARY 28 AT 10AM! </h2>\n<p> </p>\n<h3>From computer engineer to Mrs. <PERSON> to billionairess, <PERSON> sees herself as the embodiment of the American Dream. Now, as the wife of <PERSON> "The Timeshare King" <PERSON><PERSON> and mother of their eight children, she invites us to behold their most grandiose venture yet: building the largest private home in America - a $100 million house in Orlando, Florida, big enough for her dreams and inspired by the Palace of Versailles. But with the Great Recession of 2008 looming, <PERSON> and <PERSON>\'s dreams begin to crumble, along with their lavish lifestyle.<br /><br />Based on the wildly astonishing documentary directed by award-winning filmmaker <PERSON> Greenfield, <em>The Queen of <PERSON></em> reunites celebrated <PERSON>®-winning songwriter <PERSON> with <PERSON>® and Emmy Award® winner Kristin Chenoweth as <PERSON> Siegel. With a book by <PERSON> Ferre<PERSON> (<em>Ugly Lies the <PERSON>, <PERSON> and the Orphans</em>) and direction by Tony Award winner Michael Arden (<em>Parade, Once on This lsland</em>), <em>The Queen of Versailles</em> is a new musical exploring the true cost of fame, fortune, and family.</h3>\n<p> </p>\n<p style="text-align: center;"><iframe width="560" height="315" src="https://www.youtube.com/embed/YyO9q_vF-EU?si=v0r9WWjOWcivll9j" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen=""></iframe></p>',
      contentTypeAlias: 'richText',
    },
    {
      contentTypeAlias: 'cast',
      castList: [
        {
          contentTypeAlias: 'cast',
          name: 'STEPHEN SCHWARTZ',
          image: {
            src: 'https://media.umbraco.io/atg-digital/fy5n1fz0/qov_stephen-schwartz_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'Music & Lyrics',
        },
        {
          contentTypeAlias: 'cast',
          name: 'LINDSEY FERRENTINO',
          image: {
            src: 'https://media.umbraco.io/atg-digital/jxjnrrbf/qov_lindseyferrentino_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'Book',
        },
        {
          contentTypeAlias: 'cast',
          name: 'MICHAEL ARDEN',
          image: {
            src: 'https://media.umbraco.io/atg-digital/01ohrf40/qov_michaelarden_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'Director',
        },
        {
          contentTypeAlias: 'cast',
          name: 'KRISTIN CHENOWETH',
          image: {
            src: 'https://media.umbraco.io/atg-digital/qs0euujl/qov_kristinchenoweth2_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'Jackie Siegel',
        },
        {
          contentTypeAlias: 'cast',
          name: 'F. MURRAY ABRAHAM',
          image: {
            src: 'https://media.umbraco.io/atg-digital/au4kj2ss/qov_fmurrayabraham_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'David Siegel',
        },
        {
          contentTypeAlias: 'cast',
          name: 'MELODY BUTIU',
          image: {
            src: 'https://media.umbraco.io/atg-digital/cubpjtnc/qov_melodybutiu_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'Sofia Flores',
        },
        {
          contentTypeAlias: 'cast',
          name: 'NINA WHITE',
          image: {
            src: 'https://media.umbraco.io/atg-digital/k0op144f/qov_ninawhite_100x100.jpg',
            width: 100,
            height: 100,
          },
          characterName: 'Victoria Siegel',
        },
      ],
    },
    {
      contentTypeAlias: 'divider',
    },
  ],
  metadata: {
    updatedBy: 'Meredith Mastroianni',
    updatedAt: '2024-02-28T14:33:34.106Z',
    createdBy: 'Meredith Mastroianni',
    createdAt: '2024-01-29T17:50:59.473Z',
    browserTitle:
      'The Queen of Versailles | Official Box Office | Emerson Colonial Theatre',
    seoDescription:
      'Kristin Chenoweth is The Queen of Versailles. July 16 - August 18, 2024. Pre-Broadway World Premiere Musical. Emerson Colonial Theatre, Boston. From computer engineer to Mrs. Florida to billionairess, Jackie Siegel sees herself as the embodiment of the American Dream. Now, as the wife of David "The Timeshare King" Siegel and mother of their eight children, she invites us to behold their most grandiose venture yet: building the largest private home in America - a $100 million house in Orlando, Florida, big enough for her dreams and inspired by the Palace of Versailles. But with the Great Recession of 2008 looming, Jackie and David\'s dreams begin to crumble, along with their lavish lifestyle. Based on the wildly astonishing documentary directed by award-winning filmmaker Lauren Greenfield, The Queen of Versailles reunites celebrated Oscar®-winning songwriter Stephen Schwartz with Tony® and Emmy Award® winner Kristin Chenoweth as Jackie Siegel. With a book by Lindsey Ferrentino (Ugly Lies the Bone, Amy and the Orphans) and direction by Tony Award winner Michael Arden (Parade, Once on This lsland), The Queen of Versailles is a new musical exploring the true cost of fame, fortune, and family.',
    socialMetaData: [],
    entities: [],
  },
  showSlug: SHOW_SLUG,
  websiteSlug: SHOW_SLUG,
  inventorySlug: SHOW_SLUG,
  pageSlug: SHOW_SLUG,
  details: {
    genres: ['Musical'],
    title: 'The Queen of Versailles',
    firstPerformanceDate: '2024-07-16T12:00:00',
    intervals: 0,
    isSoldOut: false,
    salesPeriod:
      'JULY 16 - AUGUST 18, 2024 | TICKETS ON SALE FEBRUARY 28 AT 10AM!',
    onSaleDates: [
      {
        contentTypeAlias: 'onSaleDate',
        isSoldOut: false,
        onSaleDate: '2024-02-28T10:00:00',
        onSaleType: 'General',
        description: 'General Public On Sale',
        hideDateAndTime: false,
      },
    ],
    accessPerformances: [],
    description:
      'Ex enim pariatur elit adipisicing laboris mollit fugiat incididunt id nostrud. In aute reprehenderit minim magna minim eiusmod anim esse esse nostrud. Nostrud excepteur ex qui ullamco eu ex dolor incididunt exercitation cillum cupidatat. Sint anim commodo ullamco ullamco aute do aliquip mollit quis id eiusmod quis irure occaecat anim.\n\nLaboris elit non id ullamco et sunt tempor ad pariatur voluptate Lorem cupidatat consectetur. Enim mollit aute est duis aute incididunt ea ullamco proident. Commodo consectetur sit aute. Labore esse nulla amet.',
    resellAvailable: false,
  },
  venue: {
    id: '4kS5wbfy80uaaQoEU82Iig',
    contentTypeAlias: 'venue',
    name: 'Emerson Colonial Theatre',
    upsellTypes: [],
    slug: VENUE_SLUG,
    isATGVenue: true,
    config: {
      inventoryId: 'DFB261B5-DBC2-4601-A57D-34E41C663AED',
      venueConfigSlug: VENUE_SLUG,
      configName: 'Emerson Colonial Theatre',
      hideMembershipPromotion: false,
      isThirdParty: false,
    },
  },
  upsellList: [],
  media: {
    showCardImage: {
      src: 'https://media.umbraco.io/atg-digital/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg',
      width: 705,
      height: 405,
    },
    productionShot: {
      src: 'https://media.umbraco.io/atg-digital/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg',
      width: 1800,
      height: 600,
    },
    productionShotTall: {
      src: 'https://media.umbraco.io/atg-digital/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg',
      width: 1500,
      height: 750,
    },
  },
  settings: {
    informationBars: [],
    brandConfig: {
      availableLanguages: ['en-us'],
    },
    overrides: {
      upsells: false,
    },
    hide: {
      breadcrumbs: false,
      infoBars: false,
    },
    noIndex: false,
    forceFreeEvent: false,
    showPromoCodeBox: false,
    useSpecificQueue: false,
  },
  thirdPartyEvent: false,
}
