import type { ProductionContent } from '../../../interfaces/production'

export const productionRequestStub: ProductionContent = {
  _creatorName: '<PERSON>',
  _url: '/emersoncolonialtheatre-com/events/the-queen-of-versailles/',
  _writerName: '<PERSON>',
  _urls: {
    'en-us': '/emersoncolonialtheatre-com/events/the-queen-of-versailles/',
  },
  contentTypeAlias: 'production',
  _hasChildren: false,
  _level: 3,
  name: 'The Queen of Versailles',
  parentId: '17633f19-99d7-4ba1-986c-aba88179ba75',
  sortOrder: 50,
  _createDate: '2024-01-29T17:50:59.473Z',
  _id: 'cc33b25b-245b-4a34-b1c6-6a99b1671ac9',
  _updateDate: '2024-02-28T14:33:34.106Z',
  _links: {
    self: {
      href: 'https://cdn.umbraco.io/content/cc33b25b-245b-4a34-b1c6-6a99b1671ac9',
    },
    ancestors: {
      href: 'https://cdn.umbraco.io/content/cc33b25b-245b-4a34-b1c6-6a99b1671ac9/ancestors',
    },
    url: {
      href: 'https://cdn.umbraco.io/content/url?url=%2Femersoncolonialtheatre-com%2Fevents%2Fthe-queen-of-versailles%2F',
    },
    children: {
      href: 'https://cdn.umbraco.io/content/cc33b25b-245b-4a34-b1c6-6a99b1671ac9/children',
    },
    descendants: {
      href: 'https://cdn.umbraco.io/content/cc33b25b-245b-4a34-b1c6-6a99b1671ac9/descendants',
    },
    root: {
      href: 'https://cdn.umbraco.io/content{?contentType}',
      templated: true,
    },
    parent: {
      href: 'https://cdn.umbraco.io/content/17633f19-99d7-4ba1-986c-aba88179ba75',
    },
    showcardimage: {
      href: 'https://cdn.umbraco.io/media/6115d6ea-b590-48b4-8066-cbc801645d7a',
      title: 'QOV 003 W ECT ANNOUNCEMENT WEB GRAPHICS 020524 705X405 V2',
    },
    productionshot: {
      href: 'https://cdn.umbraco.io/media/8e6e787f-c100-4732-bb0c-f4d536421647',
      title: 'QOV 003 W ECT ANNOUNCEMENT WEB GRAPHICS 020524 1800X600 V2',
    },
    productionshottall: {
      href: 'https://cdn.umbraco.io/media/85d81db4-b9db-4fef-852b-d193fc8bf496',
      title: 'QOV 003 W ECT ANNOUNCEMENT WEB GRAPHICS 020524 1500X750 V2',
    },
    venueconfig: {
      href: 'https://cdn.umbraco.io/content/a3e5f1c6-8ea5-48d4-b78d-812b0d96ec6d',
      title: 'Emerson Colonial Theatre',
    },
  },
  genreList: ['Musical'],
  owner: null,
  venue: '',
  blocks: [
    {
      content: {
        contentTypeAlias: 'singleColumnRichText',
        richTextContent:
          '<h2 style="text-align: left;">GENERAL PUBLIC ON SALE BEGINS WEDNESDAY, FEBRUARY 28 AT 10AM! </h2>\n<p> </p>\n<h3>From computer engineer to Mrs. Florida to billionairess, Jackie Siegel sees herself as the embodiment of the American Dream. Now, as the wife of David "The Timeshare King" Siegel and mother of their eight children, she invites us to behold their most grandiose venture yet: building the largest private home in America - a $100 million house in Orlando, Florida, big enough for her dreams and inspired by the Palace of Versailles. But with the Great Recession of 2008 looming, Jackie and David\'s dreams begin to crumble, along with their lavish lifestyle.<br /><br />Based on the wildly astonishing documentary directed by award-winning filmmaker Lauren Greenfield, <em>The Queen of Versailles</em> reunites celebrated Oscar®-winning songwriter Stephen Schwartz with Tony® and Emmy Award® winner Kristin Chenoweth as Jackie Siegel. With a book by Lindsey Ferrentino (<em>Ugly Lies the Bone, Amy and the Orphans</em>) and direction by Tony Award winner Michael Arden (<em>Parade, Once on This lsland</em>), <em>The Queen of Versailles</em> is a new musical exploring the true cost of fame, fortune, and family.</h3>\n<p> </p>\n<p style="text-align: center;"><iframe width="560" height="315" src="https://www.youtube.com/embed/YyO9q_vF-EU?si=v0r9WWjOWcivll9j" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen=""></iframe></p>',
      },
    },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/97dd3e28-303a-4471-a6b4-ff345d2bf9ed',
            title: 'Qov Stephen Schwartz 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'STEPHEN SCHWARTZ',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/fy5n1fz0/qov_stephen-schwartz_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Stephen Schwartz 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 4,
          _createDate: '2024-01-29T18:51:35.033Z',
          _id: '97dd3e28-303a-4471-a6b4-ff345d2bf9ed',
          _updateDate: '2024-01-29T18:51:35.033Z',
          altTag: '',
          umbracoFile: {
            src: '/media/fy5n1fz0/qov_stephen-schwartz_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/fy5n1fz0/qov_stephen-schwartz_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/fy5n1fz0/qov_stephen-schwartz_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/fy5n1fz0/qov_stephen-schwartz_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/fy5n1fz0/qov_stephen-schwartz_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 11693,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'Music & Lyrics',
      },
    },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/c4d8d34b-cf25-4343-b539-df940ae7489b',
            title: 'Qov Lindseyferrentino 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'LINDSEY FERRENTINO',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/jxjnrrbf/qov_lindseyferrentino_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Lindseyferrentino 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 2,
          _createDate: '2024-01-29T18:51:34.173Z',
          _id: 'c4d8d34b-cf25-4343-b539-df940ae7489b',
          _updateDate: '2024-01-29T18:51:34.173Z',
          altTag: '',
          umbracoFile: {
            src: '/media/jxjnrrbf/qov_lindseyferrentino_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/jxjnrrbf/qov_lindseyferrentino_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/jxjnrrbf/qov_lindseyferrentino_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/jxjnrrbf/qov_lindseyferrentino_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/jxjnrrbf/qov_lindseyferrentino_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 12419,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'Book',
      },
    },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/9778ddd0-c8da-4be6-b558-f0f313b1356d',
            title: 'Qov Michaelarden 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'MICHAEL ARDEN',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/01ohrf40/qov_michaelarden_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Michaelarden 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 3,
          _createDate: '2024-01-29T18:51:34.613Z',
          _id: '9778ddd0-c8da-4be6-b558-f0f313b1356d',
          _updateDate: '2024-01-29T18:51:34.613Z',
          altTag: '',
          umbracoFile: {
            src: '/media/01ohrf40/qov_michaelarden_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/01ohrf40/qov_michaelarden_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/01ohrf40/qov_michaelarden_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/01ohrf40/qov_michaelarden_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/01ohrf40/qov_michaelarden_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 9611,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'Director',
      },
    },
    { content: { contentTypeAlias: 'divider' } },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/514ab482-b72b-4fa8-b134-54c67095abaa',
            title: 'Qov Kristinchenoweth2 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'KRISTIN CHENOWETH',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/qs0euujl/qov_kristinchenoweth2_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Kristinchenoweth2 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 5,
          _createDate: '2024-01-29T20:29:14.847Z',
          _id: '514ab482-b72b-4fa8-b134-54c67095abaa',
          _updateDate: '2024-01-29T20:29:14.847Z',
          altTag: '',
          umbracoFile: {
            src: '/media/qs0euujl/qov_kristinchenoweth2_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/qs0euujl/qov_kristinchenoweth2_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/qs0euujl/qov_kristinchenoweth2_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/qs0euujl/qov_kristinchenoweth2_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/qs0euujl/qov_kristinchenoweth2_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 14497,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'Jackie Siegel',
      },
    },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/f2a43c03-4452-4c18-8637-4c6e942b893f',
            title: 'Qov Fmurrayabraham 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'F. MURRAY ABRAHAM',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/au4kj2ss/qov_fmurrayabraham_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Fmurrayabraham 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 9,
          _createDate: '2024-02-06T20:47:30.177Z',
          _id: 'f2a43c03-4452-4c18-8637-4c6e942b893f',
          _updateDate: '2024-02-06T20:47:30.177Z',
          altTag: '',
          umbracoFile: {
            src: '/media/au4kj2ss/qov_fmurrayabraham_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/au4kj2ss/qov_fmurrayabraham_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/au4kj2ss/qov_fmurrayabraham_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/au4kj2ss/qov_fmurrayabraham_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/au4kj2ss/qov_fmurrayabraham_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 7223,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'David Siegel',
      },
    },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/cdf40213-90a2-4f51-96c2-160c097010a2',
            title: 'Qov Melodybutiu 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'MELODY BUTIU',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/cubpjtnc/qov_melodybutiu_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Melodybutiu 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 10,
          _createDate: '2024-02-06T20:47:30.957Z',
          _id: 'cdf40213-90a2-4f51-96c2-160c097010a2',
          _updateDate: '2024-02-06T20:47:30.957Z',
          altTag: '',
          umbracoFile: {
            src: '/media/cubpjtnc/qov_melodybutiu_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/cubpjtnc/qov_melodybutiu_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/cubpjtnc/qov_melodybutiu_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/cubpjtnc/qov_melodybutiu_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/cubpjtnc/qov_melodybutiu_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 11524,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'Sofia Flores',
      },
    },
    {
      content: {
        _links: {
          castimage: {
            href: 'https://cdn.umbraco.io/media/fbfd9d50-12c5-4356-af1a-cd540562e3f5',
            title: 'Qov Ninawhite 100X100',
          },
        },
        contentTypeAlias: 'cast',
        castName: 'NINA WHITE',
        castImage: {
          mediaTypeAlias: 'Image',
          _url: 'https://media.umbraco.io/atg-digital/k0op144f/qov_ninawhite_100x100.jpg',
          _hasChildren: false,
          _level: 4,
          name: 'Qov Ninawhite 100X100',
          parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
          sortOrder: 11,
          _createDate: '2024-02-06T20:47:31.487Z',
          _id: 'fbfd9d50-12c5-4356-af1a-cd540562e3f5',
          _updateDate: '2024-02-06T20:47:31.487Z',
          altTag: '',
          umbracoFile: {
            src: '/media/k0op144f/qov_ninawhite_100x100.jpg',
            crops: [
              {
                alias: 'Logo (Rectangle)',
                width: 250,
                height: 100,
                coordinates: null,
              },
              { alias: 'Banner', width: 250, height: 250, coordinates: null },
              {
                alias: 'Logo (Square)',
                width: 100,
                height: 100,
                coordinates: null,
              },
            ],
            cropUrls: {
              'Banner':
                'https://media.umbraco.io/atg-digital/k0op144f/qov_ninawhite_100x100.jpg?anchor=center&mode=crop&width=250&height=250',
              'Logo (Square)':
                'https://media.umbraco.io/atg-digital/k0op144f/qov_ninawhite_100x100.jpg?anchor=center&mode=crop&width=100&height=100',
              'Logo (Rectangle)':
                'https://media.umbraco.io/atg-digital/k0op144f/qov_ninawhite_100x100.jpg?anchor=center&mode=crop&width=250&height=100',
            },
            focalPoint: null,
            focalPointUrlTemplate:
              'https://media.umbraco.io/atg-digital/k0op144f/qov_ninawhite_100x100.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          umbracoBytes: 15690,
          umbracoWidth: 100,
          umbracoHeight: 100,
          umbracoExtension: 'jpg',
        },
        characterName: 'Victoria Siegel',
      },
    },
  ],
  noIndex: false,
  entities: [],
  pageName: '',
  showSlug: 'the-queen-of-versailles',
  websiteSlug: 'the-queen-of-versailles',
  doorsOpen: '',
  eventName: 'The Queen of Versailles',
  intervals: 0,
  isSoldOut: false,
  startTime: '',
  upsellList: [],
  ctaOverride: '',
  embargoDate: '0001-01-01T00:00:00',
  externalURL: '',
  onSaleDates: [
    {
      contentTypeAlias: 'onSaleDate',
      soldOut: false,
      promoCode: '',
      onSaleDate: '2024-02-28T10:00:00',
      onSaleType: 'General',
      description: 'General Public On Sale',
      offSaleDate: '0001-01-01T00:00:00',
      hideDateAndTime: false,
    },
  ],
  runningTime: '',
  salesPeriod:
    'JULY 16 - AUGUST 18, 2024 | TICKETS ON SALE FEBRUARY 28 AT 10AM!',
  venueConfig: {
    _creatorName: 'Genevieve Frey',
    _url: '/emersoncolonialtheatre-com/emerson-colonial-theatre/',
    _writerName: 'Genevieve Frey',
    _urls: { 'en-us': '/emersoncolonialtheatre-com/emerson-colonial-theatre/' },
    contentTypeAlias: 'venueConfig',
    _hasChildren: false,
    _level: 2,
    name: 'Emerson Colonial Theatre',
    parentId: '240e312b-b8e6-46d7-b89b-ca5394f96611',
    sortOrder: 8,
    _createDate: '2023-04-07T21:41:00.863Z',
    _id: 'a3e5f1c6-8ea5-48d4-b78d-812b0d96ec6d',
    _updateDate: '2023-06-02T11:01:28.201Z',
    _links: {
      seatmap: {
        href: 'https://cdn.umbraco.io/media/0863eb90-627f-4f7b-a235-cabb97d56b79',
        title: 'Colonial_SeatMap_042523',
      },
    },
    seatMap: null,
    venueSlug: 'emerson-colonial-theatre',
    inventoryID: 'DFB261B5-DBC2-4601-A57D-34E41C663AED',
    venueConfigName: 'Emerson Colonial Theatre',
    contentfulVenueId: '4kS5wbfy80uaaQoEU82Iig',
  },
  browserTitle:
    'The Queen of Versailles | Official Box Office | Emerson Colonial Theatre',
  eventSubName: '',
  hideInfoBars: false,
  twitterImage: null,
  twitterTitle: '',
  additionalCTA: '',
  showCardImage: {
    mediaTypeAlias: 'Image',
    _url: 'https://media.umbraco.io/atg-digital/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg',
    _hasChildren: false,
    _level: 4,
    name: 'QOV 003 W ECT ANNOUNCEMENT WEB GRAPHICS 020524 705X405 V2',
    parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
    sortOrder: 6,
    _createDate: '2024-02-02T19:43:37.303Z',
    _id: '6115d6ea-b590-48b4-8066-cbc801645d7a',
    _updateDate: '2024-02-02T19:43:37.303Z',
    altTag: '',
    umbracoFile: {
      src: '/media/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg',
      crops: [
        {
          alias: 'Logo (Rectangle)',
          width: 250,
          height: 100,
          coordinates: null,
        },
        { alias: 'Banner', width: 250, height: 250, coordinates: null },
        { alias: 'Logo (Square)', width: 100, height: 100, coordinates: null },
      ],
      cropUrls: {
        'Banner':
          'https://media.umbraco.io/atg-digital/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg?anchor=center&mode=crop&width=250&height=250',
        'Logo (Square)':
          'https://media.umbraco.io/atg-digital/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg?anchor=center&mode=crop&width=100&height=100',
        'Logo (Rectangle)':
          'https://media.umbraco.io/atg-digital/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg?anchor=center&mode=crop&width=250&height=100',
      },
      focalPoint: null,
      focalPointUrlTemplate:
        'https://media.umbraco.io/atg-digital/3tlbkymq/qov_003_w_ect_announcement_web_graphics_020524_705x405_v2.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
    },
    umbracoBytes: 93214,
    umbracoWidth: 705,
    umbracoHeight: 405,
    umbracoExtension: 'jpg',
  },
  showPageLabel: '',
  forceFreeEvent: false,
  lastReviewDate: '0001-01-01T00:00:00',
  nextReviewDate: '0001-01-01T00:00:00',
  openGraphImage: null,
  openGraphTitle: '',
  productionShot: {
    mediaTypeAlias: 'Image',
    _url: 'https://media.umbraco.io/atg-digital/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg',
    _hasChildren: false,
    _level: 4,
    name: 'QOV 003 W ECT ANNOUNCEMENT WEB GRAPHICS 020524 1800X600 V2',
    parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
    sortOrder: 8,
    _createDate: '2024-02-02T19:43:39.397Z',
    _id: '8e6e787f-c100-4732-bb0c-f4d536421647',
    _updateDate: '2024-02-02T19:43:39.397Z',
    altTag: '',
    umbracoFile: {
      src: '/media/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg',
      crops: [
        {
          alias: 'Logo (Rectangle)',
          width: 250,
          height: 100,
          coordinates: null,
        },
        { alias: 'Banner', width: 250, height: 250, coordinates: null },
        { alias: 'Logo (Square)', width: 100, height: 100, coordinates: null },
      ],
      cropUrls: {
        'Banner':
          'https://media.umbraco.io/atg-digital/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg?anchor=center&mode=crop&width=250&height=250',
        'Logo (Square)':
          'https://media.umbraco.io/atg-digital/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg?anchor=center&mode=crop&width=100&height=100',
        'Logo (Rectangle)':
          'https://media.umbraco.io/atg-digital/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg?anchor=center&mode=crop&width=250&height=100',
      },
      focalPoint: null,
      focalPointUrlTemplate:
        'https://media.umbraco.io/atg-digital/pf2g3dqa/qov_003_w_ect_announcement_web_graphics_020524_1800x600_v2.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
    },
    umbracoBytes: 162544,
    umbracoWidth: 1800,
    umbracoHeight: 600,
    umbracoExtension: 'jpg',
  },
  seoDescription:
    'Kristin Chenoweth is The Queen of Versailles. July 16 - August 18, 2024. Pre-Broadway World Premiere Musical. Emerson Colonial Theatre, Boston. From computer engineer to Mrs. Florida to billionairess, Jackie Siegel sees herself as the embodiment of the American Dream. Now, as the wife of David "The Timeshare King" Siegel and mother of their eight children, she invites us to behold their most grandiose venture yet: building the largest private home in America - a $100 million house in Orlando, Florida, big enough for her dreams and inspired by the Palace of Versailles. But with the Great Recession of 2008 looming, Jackie and David\'s dreams begin to crumble, along with their lavish lifestyle. Based on the wildly astonishing documentary directed by award-winning filmmaker Lauren Greenfield, The Queen of Versailles reunites celebrated Oscar®-winning songwriter Stephen Schwartz with Tony® and Emmy Award® winner Kristin Chenoweth as Jackie Siegel. With a book by Lindsey Ferrentino (Ugly Lies the Bone, Amy and the Orphans) and direction by Tony Award winner Michael Arden (Parade, Once on This lsland), The Queen of Versailles is a new musical exploring the true cost of fame, fortune, and family.',
  buyTicketsLabel: '',
  informationBars: [],
  overrideQueueID: '',
  overrideUpsells: false,
  additionalCTAURL: null,
  groupInformation: '',
  eventDescription:
    'Ex enim pariatur elit adipisicing laboris mollit fugiat incididunt id nostrud. In aute reprehenderit minim magna minim eiusmod anim esse esse nostrud. Nostrud excepteur ex qui ullamco eu ex dolor incididunt exercitation cillum cupidatat. Sint anim commodo ullamco ullamco aute do aliquip mollit quis id eiusmod quis irure occaecat anim.\n\nLaboris elit non id ullamco et sunt tempor ad pariatur voluptate Lorem cupidatat consectetur. Enim mollit aute est duis aute incididunt ea ullamco proident. Commodo consectetur sit aute. Labore esse nulla amet.',
  performanceTimes: '',
  showPromoCodeBox: false,
  useSpecificQueue: false,
  accessPerformances: [],
  pricingDescription: '',
  productionShotTall: {
    mediaTypeAlias: 'Image',
    _url: 'https://media.umbraco.io/atg-digital/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg',
    _hasChildren: false,
    _level: 4,
    name: 'QOV 003 W ECT ANNOUNCEMENT WEB GRAPHICS 020524 1500X750 V2',
    parentId: 'de83ae33-08ce-42ce-abfb-4415921d7924',
    sortOrder: 7,
    _createDate: '2024-02-02T19:43:38.320Z',
    _id: '85d81db4-b9db-4fef-852b-d193fc8bf496',
    _updateDate: '2024-02-02T19:43:38.320Z',
    altTag: '',
    umbracoFile: {
      src: '/media/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg',
      crops: [
        {
          alias: 'Logo (Rectangle)',
          width: 250,
          height: 100,
          coordinates: null,
        },
        { alias: 'Banner', width: 250, height: 250, coordinates: null },
        { alias: 'Logo (Square)', width: 100, height: 100, coordinates: null },
      ],
      cropUrls: {
        'Banner':
          'https://media.umbraco.io/atg-digital/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg?anchor=center&mode=crop&width=250&height=250',
        'Logo (Square)':
          'https://media.umbraco.io/atg-digital/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg?anchor=center&mode=crop&width=100&height=100',
        'Logo (Rectangle)':
          'https://media.umbraco.io/atg-digital/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg?anchor=center&mode=crop&width=250&height=100',
      },
      focalPoint: null,
      focalPointUrlTemplate:
        'https://media.umbraco.io/atg-digital/wionrbo1/qov_003_w_ect_announcement_web_graphics_020524_1500x750_v2.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
    },
    umbracoBytes: 193759,
    umbracoWidth: 1500,
    umbracoHeight: 750,
    umbracoExtension: 'jpg',
  },
  twitterDescription: '',
  hideFromBreadcrumbs: false,
  showPageUrlOverride: null,
  warningsAndGuidance: '',
  firstPerformanceDate: '2024-07-16T12:00:00',
  openGraphDescription: '',
  buyTicketsUrlOverride: null,
  contentManagementNotes: '',
  forcePostponedOrCanceled: '',
  checkoutCalendarVerticalImage: null,
  checkoutCalendarHorizontalImage: null,
  resellAvailable: false,
  thirdPartyEvent: false,
}
