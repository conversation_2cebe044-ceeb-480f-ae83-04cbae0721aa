export const backfillProductionCache = {
  executionId: {
    S: '5e4683d9-33f4-418b-8efb-17c43ff02137',
  },
  id: {
    S: '1b74afa0-b85f-4da4-ae6f-5c500a2f57be',
  },
  accessPerformances: {
    L: [],
  },
  additionalCTA: {
    S: '',
  },
  additionalCTAURL: {
    NULL: true,
  },
  blocks: {
    L: [
      {
        M: {
          content: {
            M: {
              contentTypeAlias: {
                S: 'singleColumnRichText',
              },
              richTextContent: {
                S: '<p>Come join <PERSON><PERSON><PERSON> on an exciting camping trip in the woods with <PERSON> and her school friends, including <PERSON>, <PERSON><PERSON> and <PERSON>. With lunchboxes packed and <PERSON> driving the bus, <PERSON><PERSON><PERSON> and friends are excited about their outdoor adventure, full of singing, dancing, games and surprises. Little piggies everywhere will love this 60-minute live musical experience!</p>',
              },
            },
          },
        },
      },
    ],
  },
  browserTitle: {
    S: "P<PERSON>pa Pig's Sing Along Party | Official Box Office | Majestic Theatre",
  },
  buyTicketsLabel: {
    S: '',
  },
  buyTicketsUrlOverride: {
    NULL: true,
  },
  checkoutCalendarHorizontalImage: {
    NULL: true,
  },
  checkoutCalendarVerticalImage: {
    M: {
      altTag: {
        S: '',
      },
      mediaTypeAlias: {
        S: 'Image',
      },
      name: {
        S: 'Majestic Peppapig Website Heromobile 390X500',
      },
      parentId: {
        S: 'fe9ceb8e-9dc0-45dd-94ae-30c7c1ac3814',
      },
      sortOrder: {
        N: '39',
      },
      umbracoBytes: {
        N: '136557',
      },
      umbracoExtension: {
        S: 'jpg',
      },
      umbracoFile: {
        M: {
          crops: {
            L: [
              {
                M: {
                  alias: {
                    S: 'Logo (Rectangle)',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '100',
                  },
                  width: {
                    N: '250',
                  },
                },
              },
              {
                M: {
                  alias: {
                    S: 'Banner',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '250',
                  },
                  width: {
                    N: '250',
                  },
                },
              },
              {
                M: {
                  alias: {
                    S: 'Logo (Square)',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '100',
                  },
                  width: {
                    N: '100',
                  },
                },
              },
            ],
          },
          cropUrls: {
            M: {
              'Banner': {
                S: 'https://media.umbraco.io/atg-digital/1rsdaahd/majestic-peppapig-website-heromobile-390x500.jpg?anchor=center&mode=crop&width=250&height=250',
              },
              'Logo (Rectangle)': {
                S: 'https://media.umbraco.io/atg-digital/1rsdaahd/majestic-peppapig-website-heromobile-390x500.jpg?anchor=center&mode=crop&width=250&height=100',
              },
              'Logo (Square)': {
                S: 'https://media.umbraco.io/atg-digital/1rsdaahd/majestic-peppapig-website-heromobile-390x500.jpg?anchor=center&mode=crop&width=100&height=100',
              },
            },
          },
          focalPoint: {
            NULL: true,
          },
          focalPointUrlTemplate: {
            S: 'https://media.umbraco.io/atg-digital/1rsdaahd/majestic-peppapig-website-heromobile-390x500.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          src: {
            S: '/media/1rsdaahd/majestic-peppapig-website-heromobile-390x500.jpg',
          },
        },
      },
      umbracoHeight: {
        N: '500',
      },
      umbracoWidth: {
        N: '390',
      },
      _createDate: {
        S: '2023-05-12T16:30:26.037Z',
      },
      _hasChildren: {
        BOOL: false,
      },
      _id: {
        S: '003065da-82e3-4c89-ad37-1fbfd1c48100',
      },
      _level: {
        N: '2',
      },
      _updateDate: {
        S: '2023-05-12T16:30:26.037Z',
      },
      _url: {
        S: 'https://media.umbraco.io/atg-digital/1rsdaahd/majestic-peppapig-website-heromobile-390x500.jpg',
      },
    },
  },
  contentManagementNotes: {
    S: '',
  },
  contentTypeAlias: {
    S: 'production',
  },
  ctaOverride: {
    S: '',
  },
  doorsOpen: {
    S: '1:00 PM',
  },
  embargoDate: {
    S: '0001-01-01T00:00:00',
  },
  eventName: {
    S: "Peppa Pig's Sing Along Party",
  },
  eventSubName: {
    S: '',
  },
  externalURL: {
    S: '',
  },
  firstPerformanceDate: {
    S: '0001-01-01T00:00:00',
  },
  forceFreeEvent: {
    BOOL: false,
  },
  forcePostponedOrCanceled: {
    S: '',
  },
  genreList: {
    L: [
      {
        S: 'Family',
      },
    ],
  },
  groupInformation: {
    S: '',
  },
  hideFromBreadcrumbs: {
    BOOL: false,
  },
  hideInfoBars: {
    BOOL: false,
  },
  informationBars: {
    L: [],
  },
  intervals: {
    N: '0',
  },
  isSoldOut: {
    BOOL: false,
  },
  lastReviewDate: {
    S: '0001-01-01T00:00:00',
  },
  name: {
    S: 'Peppa Pigs Sing Along Party',
  },
  nextReviewDate: {
    S: '0001-01-01T00:00:00',
  },
  noIndex: {
    BOOL: false,
  },
  entities: {
    L: [],
  },
  onSaleDates: {
    L: [
      {
        M: {
          contentTypeAlias: {
            S: 'onSaleDate',
          },
          description: {
            S: 'Artist Presale',
          },
          hideDateAndTime: {
            BOOL: false,
          },
          offSaleDate: {
            S: '0001-01-01T00:00:00',
          },
          onSaleDate: {
            S: '2023-05-16T12:00:00',
          },
          onSaleType: {
            S: 'Promo/Access',
          },
          promoCode: {
            S: 'PARTY',
          },
          soldOut: {
            BOOL: false,
          },
        },
      },
      {
        M: {
          contentTypeAlias: {
            S: 'onSaleDate',
          },
          description: {
            S: 'Venue Presale',
          },
          hideDateAndTime: {
            BOOL: false,
          },
          offSaleDate: {
            S: '0001-01-01T00:00:00',
          },
          onSaleDate: {
            S: '2023-05-17T10:00:00',
          },
          onSaleType: {
            S: 'Promo/Access',
          },
          promoCode: {
            S: 'SUZY',
          },
          soldOut: {
            BOOL: false,
          },
        },
      },
      {
        M: {
          contentTypeAlias: {
            S: 'onSaleDate',
          },
          description: {
            S: 'General on sale',
          },
          hideDateAndTime: {
            BOOL: false,
          },
          offSaleDate: {
            S: '0001-01-01T00:00:00',
          },
          onSaleDate: {
            S: '2023-05-19T10:00:00',
          },
          onSaleType: {
            S: 'General',
          },
          promoCode: {
            S: '',
          },
          soldOut: {
            BOOL: false,
          },
        },
      },
    ],
  },
  openGraphDescription: {
    S: '',
  },
  openGraphImage: {
    NULL: true,
  },
  openGraphTitle: {
    S: '',
  },
  overrideQueueID: {
    S: '',
  },
  overrideUpsells: {
    BOOL: false,
  },
  owner: {
    NULL: true,
  },
  pageName: {
    S: '',
  },
  parentId: {
    S: 'cba465eb-162f-481e-b23b-acf14ee7a9ab',
  },
  performanceTimes: {
    S: '',
  },
  pricingDescription: {
    S: 'For groups of 10 or more call: (210) 226-5967, Mon-Fri 10am-5pm',
  },
  productionShot: {
    M: {
      altTag: {
        S: '',
      },
      mediaTypeAlias: {
        S: 'Image',
      },
      name: {
        S: 'Majestic Peppapig Website Herodesktop 1800X600',
      },
      parentId: {
        S: 'fe9ceb8e-9dc0-45dd-94ae-30c7c1ac3814',
      },
      sortOrder: {
        N: '38',
      },
      umbracoBytes: {
        N: '348904',
      },
      umbracoExtension: {
        S: 'jpg',
      },
      umbracoFile: {
        M: {
          crops: {
            L: [
              {
                M: {
                  alias: {
                    S: 'Logo (Rectangle)',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '100',
                  },
                  width: {
                    N: '250',
                  },
                },
              },
              {
                M: {
                  alias: {
                    S: 'Banner',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '250',
                  },
                  width: {
                    N: '250',
                  },
                },
              },
              {
                M: {
                  alias: {
                    S: 'Logo (Square)',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '100',
                  },
                  width: {
                    N: '100',
                  },
                },
              },
            ],
          },
          cropUrls: {
            M: {
              'Banner': {
                S: 'https://media.umbraco.io/atg-digital/cupbxloz/majestic-peppapig-website-herodesktop-1800x600.jpg?anchor=center&mode=crop&width=250&height=250',
              },
              'Logo (Rectangle)': {
                S: 'https://media.umbraco.io/atg-digital/cupbxloz/majestic-peppapig-website-herodesktop-1800x600.jpg?anchor=center&mode=crop&width=250&height=100',
              },
              'Logo (Square)': {
                S: 'https://media.umbraco.io/atg-digital/cupbxloz/majestic-peppapig-website-herodesktop-1800x600.jpg?anchor=center&mode=crop&width=100&height=100',
              },
            },
          },
          focalPoint: {
            NULL: true,
          },
          focalPointUrlTemplate: {
            S: 'https://media.umbraco.io/atg-digital/cupbxloz/majestic-peppapig-website-herodesktop-1800x600.jpg?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          src: {
            S: '/media/cupbxloz/majestic-peppapig-website-herodesktop-1800x600.jpg',
          },
        },
      },
      umbracoHeight: {
        N: '600',
      },
      umbracoWidth: {
        N: '1800',
      },
      _createDate: {
        S: '2023-05-12T16:30:25.177Z',
      },
      _hasChildren: {
        BOOL: false,
      },
      _id: {
        S: 'ad1b1e13-a0d9-46d6-a6d8-937b741c8d71',
      },
      _level: {
        N: '2',
      },
      _updateDate: {
        S: '2023-05-12T16:30:25.177Z',
      },
      _url: {
        S: 'https://media.umbraco.io/atg-digital/cupbxloz/majestic-peppapig-website-herodesktop-1800x600.jpg',
      },
    },
  },
  productionShotTall: {
    NULL: true,
  },
  runningTime: {
    S: '',
  },
  salesPeriod: {
    S: 'Sunday, October 8th',
  },
  seoDescription: {
    S: "Peppa Pig's Sing Along Party coming to the Majestic Theatre on Sunday, October 8th. Buy tickets at the official box office. ",
  },
  showCardImage: {
    M: {
      altTag: {
        S: '',
      },
      mediaTypeAlias: {
        S: 'Image',
      },
      name: {
        S: 'Peppapig Showcard 720X405',
      },
      parentId: {
        S: 'f4b62730-4947-470e-8a1d-c6a9dd35a61a',
      },
      sortOrder: {
        N: '8',
      },
      umbracoBytes: {
        N: '271743',
      },
      umbracoExtension: {
        S: 'png',
      },
      umbracoFile: {
        M: {
          crops: {
            L: [
              {
                M: {
                  alias: {
                    S: 'Logo (Rectangle)',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '100',
                  },
                  width: {
                    N: '250',
                  },
                },
              },
              {
                M: {
                  alias: {
                    S: 'Banner',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '250',
                  },
                  width: {
                    N: '250',
                  },
                },
              },
              {
                M: {
                  alias: {
                    S: 'Logo (Square)',
                  },
                  coordinates: {
                    NULL: true,
                  },
                  height: {
                    N: '100',
                  },
                  width: {
                    N: '100',
                  },
                },
              },
            ],
          },
          cropUrls: {
            M: {
              'Banner': {
                S: 'https://media.umbraco.io/atg-digital/32torqhl/peppapig_showcard_720x405.png?anchor=center&mode=crop&width=250&height=250',
              },
              'Logo (Rectangle)': {
                S: 'https://media.umbraco.io/atg-digital/32torqhl/peppapig_showcard_720x405.png?anchor=center&mode=crop&width=250&height=100',
              },
              'Logo (Square)': {
                S: 'https://media.umbraco.io/atg-digital/32torqhl/peppapig_showcard_720x405.png?anchor=center&mode=crop&width=100&height=100',
              },
            },
          },
          focalPoint: {
            NULL: true,
          },
          focalPointUrlTemplate: {
            S: 'https://media.umbraco.io/atg-digital/32torqhl/peppapig_showcard_720x405.png?width={width}&height={height}&mode=crop&center=0.5,0.5',
          },
          src: {
            S: '/media/32torqhl/peppapig_showcard_720x405.png',
          },
        },
      },
      umbracoHeight: {
        N: '405',
      },
      umbracoWidth: {
        N: '720',
      },
      _createDate: {
        S: '2023-06-29T09:58:40.003Z',
      },
      _hasChildren: {
        BOOL: false,
      },
      _id: {
        S: 'c0e826e9-7deb-4b14-a99b-358ddbe50e08',
      },
      _level: {
        N: '4',
      },
      _updateDate: {
        S: '2023-06-29T09:58:40.003Z',
      },
      _url: {
        S: 'https://media.umbraco.io/atg-digital/32torqhl/peppapig_showcard_720x405.png',
      },
    },
  },
  showPageLabel: {
    S: '',
  },
  showPageUrlOverride: {
    NULL: true,
  },
  showPromoCodeBox: {
    BOOL: false,
  },
  showSlug: {
    S: 'peppa-pigs-sing-along-party',
  },
  websiteSlug: {
    S: 'peppa-pigs-sing-along-party-website-slug',
  },
  sortOrder: {
    N: '50',
  },
  startTime: {
    S: '2:00 PM',
  },
  twitterDescription: {
    S: '',
  },
  twitterImage: {
    NULL: true,
  },
  twitterTitle: {
    S: '',
  },
  upsellList: {
    L: [],
  },
  useSpecificQueue: {
    BOOL: false,
  },
  venue: {
    S: '',
  },
  venueConfig: {
    M: {
      contentTypeAlias: {
        S: 'venueConfig',
      },
      inventoryID: {
        S: '4A865AD4-2A4D-47F9-BCFF-ECEDB32F3F9F',
      },
      contentfulVenueId: {
        S: '4kS5wbfy80uaaQoEU82Iig',
      },
      name: {
        S: 'Majestic Theatre',
      },
      parentId: {
        S: '38613d16-c70b-418d-87e0-dfaa5f387cc6',
      },
      seatMap: {
        NULL: true,
      },
      sortOrder: {
        N: '9',
      },
      venueConfigName: {
        S: 'Majestic Theatre',
      },
      venueSlug: {
        S: 'majestic-theatre',
      },
      _createDate: {
        S: '2023-01-26T18:02:06.643Z',
      },
      _creatorName: {
        S: 'Administrator',
      },
      _hasChildren: {
        BOOL: false,
      },
      _id: {
        S: '9ed66e32-eb97-40b8-8806-597a8bfadf7e',
      },
      _level: {
        N: '2',
      },
      _links: {
        M: {
          seatmap: {
            M: {
              href: {
                S: 'https://cdn.umbraco.io/media/316611f6-ff3c-4de3-9355-8303b8c68e1b',
              },
              title: {
                S: 'MajesticTheatre_SeatingChart_051023',
              },
            },
          },
        },
      },
      _updateDate: {
        S: '2023-06-02T14:35:52.696Z',
      },
      _url: {
        S: '/majesticempire-com/majestic-theatre/',
      },
      _urls: {
        M: {
          'en-us': {
            S: '/majesticempire-com/majestic-theatre/',
          },
        },
      },
      _writerName: {
        S: 'Genevieve Frey',
      },
    },
  },
  warningsAndGuidance: {
    S: 'Children ages 1+ will require a ticket',
  },
  _createDate: {
    S: '2023-05-12T16:35:59.603Z',
  },
  _creatorName: {
    S: 'Adam Fleming',
  },
  _hasChildren: {
    BOOL: false,
  },
  _id: {
    S: '1b74afa0-b85f-4da4-ae6f-5c500a2f57be',
  },
  _level: {
    N: '3',
  },
  _links: {
    M: {
      ancestors: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/1b74afa0-b85f-4da4-ae6f-5c500a2f57be/ancestors',
          },
        },
      },
      checkoutcalendarverticalimage: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/media/003065da-82e3-4c89-ad37-1fbfd1c48100',
          },
          title: {
            S: 'Majestic Peppapig Website Heromobile 390X500',
          },
        },
      },
      children: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/1b74afa0-b85f-4da4-ae6f-5c500a2f57be/children',
          },
        },
      },
      descendants: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/1b74afa0-b85f-4da4-ae6f-5c500a2f57be/descendants',
          },
        },
      },
      parent: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/cba465eb-162f-481e-b23b-acf14ee7a9ab',
          },
        },
      },
      productionshot: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/media/ad1b1e13-a0d9-46d6-a6d8-937b741c8d71',
          },
          title: {
            S: 'Majestic Peppapig Website Herodesktop 1800X600',
          },
        },
      },
      root: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content{?contentType}',
          },
          templated: {
            BOOL: true,
          },
        },
      },
      self: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/1b74afa0-b85f-4da4-ae6f-5c500a2f57be',
          },
        },
      },
      showcardimage: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/media/c0e826e9-7deb-4b14-a99b-358ddbe50e08',
          },
          title: {
            S: 'Peppapig Showcard 720X405',
          },
        },
      },
      url: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/url?url=%2Fmajesticempire-com%2Fevents%2Fpeppa-pigs-sing-along-party%2F',
          },
        },
      },
      venueconfig: {
        M: {
          href: {
            S: 'https://cdn.umbraco.io/content/9ed66e32-eb97-40b8-8806-597a8bfadf7e',
          },
          title: {
            S: 'Majestic Theatre',
          },
        },
      },
    },
  },
  _updateDate: {
    S: '2023-08-09T11:56:35.770Z',
  },
  _url: {
    S: '/majesticempire-com/events/peppa-pigs-sing-along-party/',
  },
  _urls: {
    M: {
      'en-us': {
        S: '/majesticempire-com/events/peppa-pigs-sing-along-party/',
      },
    },
  },
  _writerName: {
    S: 'Nathanael Woodbridge',
  },
}
