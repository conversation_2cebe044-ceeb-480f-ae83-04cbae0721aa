const fs = require('fs');
const http = require('http');

// Read the production stub
const stub = require('./packages/cms-listener-service/src/__stubs__/umbraco/request/production.ts');

// Prepare the request
const postData = JSON.stringify(stub.productionRequestStub);

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/development/production',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'umb-headless-event': 'content.publish',
    'content-language': 'en-US',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('Sending webhook request to http://localhost:3000/development/production');
console.log('Payload includes:');
console.log('  - showSlug:', stub.productionRequestStub.showSlug);
console.log('  - websiteSlug:', stub.productionRequestStub.websiteSlug);
console.log('  - inventorySlug:', stub.productionRequestStub.inventorySlug || '(not sent by Umbraco)');
console.log('');

const req = http.request(options, (res) => {
  console.log(`Response Status: ${res.statusCode}`);
  console.log('Response Headers:', res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('\nResponse Body:');
    try {
      const parsed = JSON.parse(data);
      console.log(JSON.stringify(parsed, null, 2));
      
      // Check if the response contains the mapped data
      if (parsed.body) {
        const body = typeof parsed.body === 'string' ? JSON.parse(parsed.body) : parsed.body;
        console.log('\n=== SLUG MAPPING VERIFICATION ===');
        console.log('showSlug:', body.showSlug || 'NOT FOUND');
        console.log('websiteSlug:', body.websiteSlug || 'NOT FOUND');
        console.log('inventorySlug:', body.inventorySlug || 'NOT FOUND');
        
        if (body.showSlug && body.websiteSlug && body.inventorySlug) {
          console.log('\n✅ SUCCESS: All three slug fields are present in the mapped output!');
        } else {
          console.log('\n❌ ISSUE: Some slug fields are missing');
        }
      }
    } catch (e) {
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.write(postData);
req.end();