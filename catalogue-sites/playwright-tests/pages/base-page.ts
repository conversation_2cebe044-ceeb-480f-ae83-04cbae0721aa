import { Locator, Page, test, expect } from "@playwright/test";
import translation from "@catalogue-sites/ui/locales/en-US/translation.json";

import { Actions } from "../helpers/actions";
import { Assertions } from "../helpers/assertions";

export abstract class BasePage {
  protected readonly page: Page;
  private readonly searchIcon: Locator;
  private readonly cancelButton: Locator;
  private readonly clearTextButton: Locator;
  private readonly searchTextBox: Locator;
  private readonly searchListBox: Locator;
  private readonly newSearchListBox: Locator;
  protected readonly pageHeader: Locator;
  private readonly theatreLogo: Locator;
  private readonly mobileSearchButton: Locator;
  private readonly firstSeeMoreButton: Locator;
  private readonly evidonAcceptCookiesButton: Locator;
  private readonly siteHeader: Locator;
  private readonly mobileSiteHeader: Locator;

  protected constructor(page: Page) {
    this.page = page;
    this.evidonAcceptCookiesButton = page.locator(
      "button#_evidon-accept-button",
    );
    this.searchIcon = page.getByRole("button", { name: "search" });
    this.cancelButton = page.getByRole("button", { name: "Cancel" });
    this.clearTextButton = page.getByRole("banner").getByRole("button").first();
    this.searchTextBox = page.getByRole("combobox", {
      name: "Search",
    });
    this.searchListBox = page.getByRole("listbox").first();
    this.newSearchListBox = this.page.getByRole("tooltip");
    this.pageHeader = page.locator("h1").first();
    this.theatreLogo = page.locator("img[alt='Theatre logo']");
    this.mobileSearchButton = page.getByRole("button", {
      name: "search",
      exact: true,
    });
    this.firstSeeMoreButton = page
      .getByRole("link", { name: /see more/i })
      .first();
    this.siteHeader = page.getByTestId("desktop-header");
    this.mobileSiteHeader = page
      .getByTestId("mobile-header")
      .locator("div")
      .filter({ hasText: "AccountLog in /" })
      .nth(1);
  }

  public async navigateToHomePage(): Promise<void> {
    test
      .info()
      .annotations.push({ type: "Url", description: process.env.BASE_URL });
    await test.step("Navigate to URL", async () => {
      await this.page.goto("/", { waitUntil: "load" });
      // await this.clickAcceptCookiesButton();
      // await this.alwaysDismissOnetrustBanner();
      await this.hideCookieBanner();
      await this.alwaysHideCookieBanner();
    });
  }

  public async navigateToListingPage(): Promise<void> {
    test.info().annotations.push({
      type: "Url",
      description: process.env.BASE_URL_LISTING,
    });
    await test.step("Navigate to URL", async () => {
      await this.page.goto(process.env.BASE_URL_LISTING, {
        waitUntil: "load",
      });
      await this.hideCookieBanner();
      await this.alwaysHideCookieBanner();
      // await this.clickAcceptCookiesButton();
      // await this.alwaysDismissOnetrustBanner();
    });
  }

  public async navigateToPage(url: string): Promise<void> {
    await this.page.goto(url, {
      waitUntil: "load",
    });
    await this.hideCookieBanner();
  }

  public async goBack(): Promise<void> {
    await this.page.goBack({ waitUntil: "load" });
    await this.page.waitForLoadState("networkidle");
    await this.hideCookieBanner();
  }

  public async hideCookieBanner(): Promise<void> {
    try {
      await this.page.waitForSelector("#onetrust-consent-sdk", {
        state: "attached",
        timeout: 5000,
      });
      await this.page.addStyleTag({
        content: `
          #onetrust-consent-sdk {
            display: none !important;
          }`,
      });
    } catch (error) {
      console.warn("Cookie banner was not found or could not be hidden:");
    }
  }

  public async alwaysHideCookieBanner(): Promise<void> {
    const bannerBtn = this.page.getByRole("button", {
      name: "Accept All Cookies",
    });

    await this.page.addLocatorHandler(bannerBtn, async () => {
      await this.hideCookieBanner();
    });
  }

  public async clickAcceptCookiesButton(): Promise<void> {
    await test.step("Click Accept Cookies Button", async () => {
      const oneTrustButton = this.page.getByRole("button", {
        name: "Accept All Cookies",
      });
      try {
        await oneTrustButton.waitFor({ state: "visible", timeout: 20000 });
        await oneTrustButton.click({ force: true });
        await oneTrustButton.waitFor({ state: "hidden", timeout: 5000 });
      } catch {}
    });
  }

  public async clickClearTextButton(): Promise<void> {
    await test.step("[Home Page] Click Clear Text Button", async () => {
      await Actions.click(this.clearTextButton);
    });
  }

  public async clickMobileSearchButton(): Promise<void> {
    await test.step("Click Mobile Search Button", async () => {
      await Actions.click(this.mobileSearchButton);
      await this.hideCookieBanner();
    });
  }

  public async enterShowName(enteredShowName: string): Promise<string> {
    return await test.step("Enter and Validate Show Name", async () => {
      test
        .info()
        .annotations.push({ type: "Show name", description: enteredShowName });
      await Actions.click(this.searchTextBox);
      await Actions.type(this.searchTextBox, enteredShowName.substring(0, 3));
      await Assertions.expectToBeVisible(
        this.page.locator(`a`, { hasText: enteredShowName }).first(),
      );
      await Assertions.expectToBeVisible(this.searchListBox);
      return enteredShowName;
    });
  }

  public async enterSearchQuery(searchQuery: string): Promise<string> {
    return await test.step("Enter Search Query", async () => {
      test
        .info()
        .annotations.push({ type: "Search Query", description: searchQuery });
      await this.searchTextBox.waitFor({ state: "visible", timeout: 10000 });
      await this.searchTextBox.click();
      await this.searchTextBox.fill(searchQuery);
      await Assertions.expectToBeVisible(this.newSearchListBox);
      return searchQuery;
    });
  }

  public async searchTextBoxShouldBeEmpty(): Promise<void> {
    await test.step("Validate Search Text Box To Be Empty", async () => {
      await Assertions.expectNotToHaveText(this.searchTextBox);
    });
  }

  public async clickSearchedShow(showName: string): Promise<void> {
    await test.step("[Home Page] Click Searched Show Name", async () => {
      await Promise.all([
        Actions.click(this.page.locator(`a`, { hasText: showName }).first()),
        this.page.waitForResponse("**/graphql"),
      ]);
    });
  }

  public async clickSearchResult(result: string): Promise<void> {
    await test.step("Click Searched Result", async () => {
      const searchResult = this.page.locator(
        `[role="tooltip"] a:has-text("${result}")`,
      );
      await searchResult.waitFor({ state: "attached", timeout: 10000 });
      await searchResult.click({ force: true });
      await this.hideCookieBanner();
    });
  }

  public async clickVenueSearchResult(venueItem: string): Promise<void> {
    await test.step(`Click Venue Search Result: "${venueItem}"`, async () => {
      const venueSearchResult = this.page.locator(
        `[role="tooltip"] a[href*="/venues/"]:has-text("${venueItem}")`,
      );
      await venueSearchResult.waitFor({ state: "visible", timeout: 10000 });
      await venueSearchResult.click({ force: true });
      await this.hideCookieBanner();
    });
  }

  public async clickSearchButton(): Promise<void> {
    await test.step("Click Search button", async () => {
      await this.searchTextBox.waitFor({ state: "visible", timeout: 10000 });
      await this.page
        .getByRole("button", { name: "Search", exact: true })
        .click();
      await this.hideCookieBanner();
    });
  }

  public async verifyDisplayedSearchResults(
    expectedResults: string | string[],
  ): Promise<void> {
    await test.step("Verify Search Results", async () => {
      const tooltip = this.page.getByRole("tooltip");
      const results = Array.isArray(expectedResults)
        ? expectedResults
        : [expectedResults];

      for (const result of results) {
        const resultLocator = tooltip.getByText(result, { exact: true });
        await expect(
          resultLocator,
          `Expected "${result}" to be visible in tooltip`,
        ).toBeVisible();
      }
    });
  }

  public async clearSearchBox(): Promise<void> {
    await test.step("Clear Search Box", async () => {
      await this.searchTextBox.waitFor({ state: "visible", timeout: 10000 });
      await this.searchTextBox.clear();
      await this.searchTextBoxShouldBeEmpty();
    });
  }

  public async clickClearSearchButton(): Promise<void> {
    await test.step("Clear Search Box By Clicking X Icon", async () => {
      await this.searchTextBox.waitFor({ state: "visible", timeout: 15000 });
      await this.searchTextBox.click();
      await this.page.getByRole("button", { name: "Clear search" }).click();
      await this.searchTextBoxShouldBeEmpty();
    });
  }

  public async verifyPageHeader(
    expectedShowPageHeader: string | RegExp,
  ): Promise<void> {
    await test.step("Validate Page Header", async () => {
      await this.page.waitForLoadState("load");
      await expect(async () => {
        const headerText = await this.pageHeader.innerText();

        if (expectedShowPageHeader instanceof RegExp) {
          expect(headerText).toMatch(expectedShowPageHeader);
        } else {
          expect(headerText).toContain(expectedShowPageHeader);
        }
      }).toPass({ timeout: 15000 });
    });
  }

  public async verifyCheckoutPage(): Promise<void> {
    await test.step("Verify Checkout Page", async () => {
      const keywordRegex = new RegExp(
        `\\/calendar\\/|\\/tickets\\/|ticketmaster|queue`,
        "i",
      );
      const containsKeyword = keywordRegex.test(
        await Actions.getPageURL(this.page),
      );
      test.info().annotations.push({
        type: "Checkout Url",
        description: await Actions.getPageURL(this.page),
      });
      expect(containsKeyword).toBeTruthy();
    });
  }

  public async verifyCurrentUrlContains(pageLink: string): Promise<void> {
    await test.step("Verify Current Url", async () => {
      await Assertions.expectToContain(this.page.url(), pageLink);
    });
  }

  public async verifyNotANoShowPage(): Promise<void> {
    await test.step("[Not A Show Page] Validate No Show Page Header", async () => {
      await Assertions.expectToNotContain(
        await Actions.getPageTextContent(this.page),
        translation.errors404.title,
      );
    });
  }

  public async navigateAndVerifyStatusCode(
    url: string,
    expectedStatusCode: number,
  ): Promise<void> {
    test.info().annotations.push({
      type: "Url",
      description: process.env.BASE_URL,
    });
    await test.step("Navigate to URL", async () => {
      const [response] = await Promise.all([
        this.page.waitForResponse(
          (res) =>
            res.request().url().includes(url) &&
            res.request().method() === "GET",
        ),
        this.page.goto(url, { waitUntil: "load" }),
        this.hideCookieBanner(),
      ]);
      expect(response.status()).toBe(expectedStatusCode);
    });
  }
  public async clickFirstSeeMoreButton(): Promise<void> {
    await test.step("Click The First See More Button", async () => {
      await Promise.all([
        Actions.click(this.firstSeeMoreButton),
        this.page.waitForLoadState("load"),
      ]);
    });
  }
  public async verifyPageTitle(expectedTitle: string | RegExp) {
    await test.step(`Verify page title matches "${expectedTitle.toString()}"`, async () => {
      await expect(this.page).toHaveTitle(expectedTitle);
    });
  }

  public async assertSiteHeaderVisibility(): Promise<void> {
    await test.step("Verify site header is fully visible and functional", async () => {
      await expect(this.siteHeader).toBeVisible();
    });
  }

  public async assertMobileSiteHeaderVisibility(): Promise<void> {
    await test.step("Verify site header is fully visible and functional", async () => {
      await expect(this.mobileSiteHeader).toBeVisible();
    });
  }
}
