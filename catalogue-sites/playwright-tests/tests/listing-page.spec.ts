import { test, Page, expect } from "@playwright/test";

import { HomePage } from "../pages/home-page";

import { ListingPage } from "pages/listing-page";

let page: Page;
let homePage: HomePage;
let listingPage: ListingPage;
const showName = `Wicked`;
const closedShowName = `James Acaster: Hecklers Welcome`;

test.describe.parallel("ATG Listing Page @ListingWeb", () => {
  test.beforeAll(async ({ browser }) => {
    const context = await browser.newContext();
    page = await context.newPage();
  });

  test.beforeEach(async () => {
    homePage = new HomePage(page);
    await homePage.navigateToListingPage();
    listingPage = new ListingPage(page);
  });

  test("Apply location filters and verify show visibility, header, and breadcrumb", async () => {
    await listingPage.assertAllLocationsSelectedByDefault();
    await listingPage.filterByLocation("London & West End");
    await listingPage.assertCurrentBreadcrumb("London & West End");
    await listingPage.verifyPageHeader("London & West End");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.filterByLocation("Liverpool");
    await listingPage.verifyPageHeader("Shows");
    await listingPage.assertCurrentBreadcrumb("Shows");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.filterByLocation("All locations");
    await listingPage.assertCurrentBreadcrumb("Shows");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.searchSelectLocation("Brighton");
    await listingPage.verifyPageHeader("Shows in Brighton");
    await listingPage.assertCurrentBreadcrumb("Brighton");
    await listingPage.assertShowNotPresentAcrossPages(showName);
  });

  test("User can filter shows by multiple access types", async () => {
    await listingPage.assertAllAccessTypeSelectedByDefault();
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.selectAccessTypeAndSubmit("Audio");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.selectAccessTypeAndSubmit("All access types");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.selectAccessTypeAndSubmit([
      "Captioned",
      "Signed",
      "Relaxed",
    ]);
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.verifyShowCard({
      showName: showName,
      accessibilityIcons: {
        audio: true,
        sign: true,
        captioned: true,
      },
    });
    await listingPage.uncheckAccessTypeAndSubmit(["Captioned", "Signed"]);
    await page.waitForURL((url) => {
      return url.searchParams.get("accessType") === "relaxed";
    });
    await listingPage.assertShowNotPresentAcrossPages(showName);
    await listingPage.selectAccessTypeAndSubmit("All access types");
    await listingPage.assertShowPresentAcrossPages(showName);
  });

  test("User can navigate between comedy sub-genres (Stand Up, Improv) with correct breadcrumb trail and headers", async () => {
    await listingPage.selectGenreTabAndVerifyUrl("Comedy");
    await listingPage.assertCurrentBreadcrumb("Comedy");
    await listingPage.verifyPageHeader("Comedy");
    await listingPage.selectSubGenreTabAndVerifyUrl("Stand Up");
    await listingPage.verifyPageHeader("Stand Up");
    await listingPage.assertCurrentBreadcrumb("Stand Up");
    await listingPage.selectSubGenreTabAndVerifyUrl("Improv");
    await listingPage.verifyPageHeader("Comedy");
    await listingPage.assertCurrentBreadcrumb("Comedy");
    await listingPage.clickGenreOrSubGenreTab("Improv");
    await listingPage.assertCurrentBreadcrumb("Stand Up");
    await listingPage.verifyPageHeader("Stand Up");
    await listingPage.clickGenreOrSubGenreTab("Stand Up");
    await listingPage.verifyPageHeader("Comedy");
    await listingPage.assertCurrentBreadcrumb("Comedy");
    await listingPage.clickBreadcrumbByNameAndVerifyLocation("Shows");
    await listingPage.assertAllGenreSelectedByDefault();
    await listingPage.assertCurrentBreadcrumb("Shows");
    await listingPage.verifyPageHeader("Shows");
    await listingPage.filterByLocation("London & West End");
    await listingPage.assertCurrentBreadcrumb("London & West End");
    await listingPage.verifyPageHeader("London & West End");
  });

  test("Test combined location and genre filtering with breadcrumb navigation", async () => {
    await listingPage.filterByLocation("London & West End");
    await listingPage.verifyPageHeader("London & West End");
    await listingPage.verifyCurrentUrlContains("/london-west-end/");
    await listingPage.selectGenreTabAndVerifyUrl("Musicals");
    await listingPage.verifyPageHeader("Musicals in London & West End");
    await listingPage.assertCurrentBreadcrumb("Musicals");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.filterByLocation("Brighton");
    await listingPage.verifyPageHeader("Musicals");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.uncheckLocation("London & West End");
    await listingPage.verifyPageHeader("Musicals in Brighton");
    await listingPage.assertCurrentBreadcrumb("Musicals");
    await listingPage.assertShowNotPresentAcrossPages(showName);
    await listingPage.clickBreadcrumbByNameAndVerifyLocation("Brighton");
    await listingPage.verifyPageHeader("Brighton");
    await listingPage.assertCurrentBreadcrumb("Brighton");
    await listingPage.assertShowNotPresentAcrossPages(showName);
    await listingPage.clickBreadcrumbByNameAndVerifyLocation("Shows");
    await listingPage.assertAllGenreSelectedByDefault();
    await listingPage.verifyPageHeader("Shows");
    await listingPage.assertCurrentBreadcrumb("Shows");
    await listingPage.assertShowPresentAcrossPages(showName);
  });

  test("Apply date filter = `This week` & time filter = `Evening` and verify specific show is listed", async () => {
    await listingPage.verifyPageHeader("Shows");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl(
      "This week",
      "Evening",
    );
    await listingPage.verifyDateAndTimeFilterBarText("This week, Evening");
    await listingPage.assertShowPresentAcrossPages("wicked");
  });

  test("Apply date filter = `Next week` and verify specific show is listed", async () => {
    await listingPage.verifyPageHeader("Shows");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl("Next week");
    await listingPage.verifyDateAndTimeFilterBarText("Next week, All times");
    await listingPage.assertShowPresentAcrossPages("wicked");
  });

  test("Apply date filter as `Date picker - Single date` and verify specific show is listed", async () => {
    await listingPage.verifyPageHeader("Shows");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl(
      "Date picker - single date",
      ["Matinee", "Evening"],
    );
    await listingPage.assertShowPresentAcrossPages("wicked");
  });

  test("Apply date filter as `Date picker - date range` & all times and verify specific show is listed", async () => {
    await listingPage.verifyPageHeader("Shows");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl(
      "Date picker - date range",
      ["Morning", "Matinee", "Evening"],
    );
    await listingPage.assertShowPresentAcrossPages("wicked");
  });

  test("Sort by Upcoming and Newest Onsale", async () => {
    await listingPage.assertSortByOptionSelected("Upcoming");
    await listingPage.selectSortByOptionAndVerifyUrl("Newest Onsale");
    await listingPage.assertSortByOptionSelected("Newest Onsale");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.goToPage(1);
    await listingPage.selectSortByOptionAndVerifyUrl("Upcoming");
    await listingPage.assertSortByOptionSelected("Upcoming");
    await listingPage.assertShowPresentAcrossPages(showName);
  });

  test("Sort by A-Z and Z-A", async () => {
    await listingPage.selectSortByOptionAndVerifyUrl("A-Z");
    await listingPage.assertSortByOptionSelected("A-Z");
    await listingPage.assertShowsAreAlphabeticallyOrdered("A-Z");
    await listingPage.selectSortByOptionAndVerifyUrl("Z-A");
    await listingPage.assertSortByOptionSelected("Z-A");
    await listingPage.assertShowsAreAlphabeticallyOrdered("Z-A");
  });

  test("Verify pagination is present and works correctly", async () => {
    await listingPage.verifyPaginationIsPresent();
    const lastPageNumber = await listingPage.getTotalNumberOfPages();
    if (lastPageNumber > 1) {
      await listingPage.clickNextPageButtonAndAssertCurrentPage(2);
      await listingPage.clickPreviousPageButtonAndAssertCurrentPage(1);
      await listingPage.clickLastPageButtonAndAssertCurrentPage(lastPageNumber);
    }
    if (lastPageNumber === 1) {
      await listingPage.checkPaginationButtonsAreDisabled();
    }
  });

  test("No results found", async () => {
    await listingPage.selectGenreTabAndVerifyUrl("Film");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl(
      "This week",
      "Morning",
    );
    await listingPage.assertNoShowsMessageAndResetFilters();
    await listingPage.assertAllGenreSelectedByDefault();
    await listingPage.assertCurrentBreadcrumb("Shows");
    await listingPage.verifyPageHeader("Shows");
  });

  test("Verify that a closed show is not listed", async () => {
    await listingPage.selectGenreTabAndVerifyUrl("Comedy");
    await listingPage.assertShowNotPresentAcrossPages(closedShowName);
  });

  test("Apply all filters (genre, location, date, access type) and verify filter persistence when navigating away and back", async () => {
    await listingPage.selectGenreTabAndVerifyUrl("Musicals");
    await listingPage.verifyPageHeader("Musicals");
    await listingPage.filterByLocation("London & West End");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl(
      "This week",
      "Evening",
    );
    await listingPage.selectAccessTypeAndSubmit("Audio");
    await listingPage.selectSortByOptionAndVerifyUrl("Z-A");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.verifyCurrentUrlContains("musicals");
    await listingPage.verifyCurrentUrlContains("london-west-end");
    await listingPage.verifyCurrentUrlContains("dateOption=thisWeek");
    await listingPage.verifyCurrentUrlContains("timeOfDay=evening");
    await listingPage.verifyCurrentUrlContains("accessType=audio");
    await listingPage.verifyCurrentUrlContains("audio&sort=z-a");
    const currentUrl = page.url();
    await listingPage.clickShowCardButton(showName, "More info");
    await listingPage.verifyCurrentUrlContains(
      `shows/${showName.toLowerCase()}/`,
    );
    await listingPage.goBack();
    await expect(page).toHaveURL(currentUrl);
    await listingPage.verifyPageHeader("Musicals in London & West End");
    await listingPage.verifyDateAndTimeFilterBarText("This week, Evening");
    await listingPage.verifyAccessTypeFilterText("Audio described");
    const tab = listingPage.getTabByName("Musicals");
    await expect(tab).toHaveAttribute("aria-selected", "true");
  });

  test("Test URL parameter handling by directly loading filtered URL", async () => {
    const baseUrl = process.env.BASE_URL_LISTING.replace(/\/uk\/?$/, "");
    await listingPage.navigateToPage(
      `${baseUrl}/london-west-end/musicals/?dateOption=thisWeek&timeOfDay=evening&accessType=audio&sort=z-a`,
    );
    await listingPage.verifyPageHeader("Musicals in London & West End");
    await listingPage.verifyDateAndTimeFilterBarText("This week, Evening");
    await listingPage.verifyAccessTypeFilterText("Audio described");
    const tab = listingPage.getTabByName("Musicals");
    await expect(tab).toHaveAttribute("aria-selected", "true");
    await listingPage.assertSortByOptionSelected("Z-A");
    await listingPage.assertShowPresentAcrossPages(showName);
  });

  test("Search with multiple filters", async ({}) => {
    await listingPage.enterSearchQuery("London");
    await listingPage.clickSearchButton();
    await page.waitForURL(/\/whats-on\/\?q=london$/i);
    await listingPage.selectGenreTabAndVerifyUrl("Musicals");
    await listingPage.filterByLocation("London & West End");
    await listingPage.selectDateAndTimeFilterAndVerifyUrl(
      "This week",
      "Evening",
    );
    await listingPage.selectAccessTypeAndSubmit("Audio");
    await listingPage.selectSortByOptionAndVerifyUrl("Z-A");
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.verifyCurrentUrlContains("musicals");
    await listingPage.verifyCurrentUrlContains("london-west-end");
    await listingPage.verifyCurrentUrlContains("dateOption=thisWeek");
    await listingPage.verifyCurrentUrlContains("timeOfDay=evening");
    await listingPage.verifyCurrentUrlContains("accessType=audio");
    await listingPage.verifyCurrentUrlContains("&sort=z-a");
    await listingPage.verifyCurrentUrlContains("q=London");
  });

  test("Search - Auto complete @ListingMobile", async ({ isMobile }) => {
    if (isMobile) await listingPage.clickMobileSearchButton();
    await listingPage.enterSearchQuery(showName.substring(0, 2));
    await listingPage.verifyDisplayedSearchResults(
      "Type at least 3 characters for results",
    );
    await listingPage.clickClearSearchButton();
    await listingPage.enterSearchQuery(showName.substring(0, 3));
    await listingPage.clickSearchResult("View all results");
    await page.waitForURL(/\/whats-on\/\?q=wic$/i);
    await listingPage.verifyPageHeader(
      new RegExp(`^\\d+ result[s]? for "Wic"`),
    );
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.clearSearchBox();
    await listingPage.enterSearchQuery("Apollo");
    await page.waitForTimeout(2000);
    await listingPage.clickSearchResult("View all results");
    await page.waitForURL(/\/whats-on\/\?q=apollo$/i);
    await listingPage.verifyPageHeader(
      new RegExp(`^\\d+ result[s]? for "Apollo"$`, "i"),
    );
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.clearSearchBox();
    await listingPage.enterSearchQuery("London");
    await listingPage.verifyDisplayedSearchResults([
      "Locations",
      "London & West End",
      "Venues",
      "Apollo Victoria Theatre",
    ]);
    await listingPage.clickSearchResult("View all results");
    await page.waitForURL(/\/whats-on\/\?q=london$/i);
    await listingPage.verifyPageHeader(
      new RegExp(`^\\d+ result[s]? for "London"`, "i"),
    );
    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.clearSearchBox();
    await listingPage.enterSearchQuery("Dear Evan Hansen");
    await listingPage.verifyDisplayedSearchResults([
      "Dear Evan Hansen",
      "Dear Evan Hansen Tour",
    ]);
    await listingPage.clearSearchBox();
    await listingPage.enterSearchQuery(showName);
    await listingPage.verifyDisplayedSearchResults(showName);
    await listingPage.clickSearchButton();
    await page.waitForURL(/\/whats-on\/\?q=wicked$/i);
    await listingPage.verifyPageHeader(
      new RegExp(`^\\d+ result[s]? for "Wicked"`, "i"),
    );
    await listingPage.assertShowPresentAcrossPages(showName);
    if (isMobile) await listingPage.clickMobileSearchButton();
    await listingPage.clearSearchBox();
    await listingPage.enterSearchQuery("NotARealshow");
    await listingPage.verifyDisplayedSearchResults("No results found");
    await listingPage.clickClearSearchButton();
    await listingPage.enterSearchQuery("NotARealshow");
    await listingPage.clickSearchButton();
    await listingPage.verifyPageHeader(`0 result for "NotARealshow"`);
    await listingPage.assertNoShowsMessageAndResetFilters();
    page.waitForTimeout(2000);
    if (isMobile) await listingPage.clickMobileSearchButton();
    await listingPage.clearSearchBox();
    // await listingPage.listingClearSearchBoxWithButton();
    await listingPage.enterSearchQuery(showName);
    await listingPage.clickSearchResult(showName);
    await page.waitForURL("**/shows/wicked/apollo-victoria-theatre/");
    if (isMobile) await listingPage.clickMobileSearchButton();
    await listingPage.enterSearchQuery("Apollo  Victoria Theatre");
    await listingPage.clickVenueSearchResult("Apollo  Victoria Theatre");
    await page.waitForURL("**/venues/apollo-victoria-theatre/whats-on/");
    if (isMobile) await listingPage.clickMobileSearchButton();
    await listingPage.enterSearchQuery("London");
    await listingPage.clickSearchResult("London & West End");
    await page.waitForURL("**/whats-on/london-west-end/");
    await listingPage.verifyPageHeader("Shows in London & West End");
    await listingPage.assertShowPresentAcrossPages(showName);
  });

  test("Verify listing page UI, genre filter, show visibility, and navigation actions,filter persistence when navigating away and back", async () => {
    await listingPage.verifyPageTitle(
      "What's On – London & UK Theatre Shows | ATG Tickets",
    );
    await listingPage.assertSiteHeaderVisibility();
    await listingPage.assertCurrentBreadcrumb("Shows");
    await listingPage.verifyPageHeader("Shows");
    const allShowCount = await listingPage.validateShowsCountDisplayed();
    await listingPage.assertDefaultSortIsUpcoming();
    await listingPage.assertAllAccessTypeSelectedByDefault();
    await listingPage.assertAllGenreSelectedByDefault();
    await listingPage.checkPreviousButtonIsDisabledOnFirstLoad();
    await listingPage.selectGenreTabAndVerifyUrl("Musicals");
    await listingPage.verifyPageHeader("Musicals");
    await listingPage.assertCurrentBreadcrumb("Musicals");
    const musicalShowCount = await listingPage.validateShowsCountDisplayed();

    expect(musicalShowCount).toBeLessThan(allShowCount);

    await listingPage.assertShowPresentAcrossPages(showName);
    await listingPage.verifyShowCard({
      showName: showName,
      genre: "Musicals",
      venue: "Apollo Victoria Theatre",
      dateRange: "Until Sun 4 Jan 2026",
      minPrice: "From £25.00",
    });

    await listingPage.clickShowCardButton(showName, "Buy tickets");
    await listingPage.verifyCheckoutPage();
    await listingPage.goBack();
    await listingPage.clickShowCardButton(showName, "More info");
    await listingPage.verifyCurrentUrlContains(
      `shows/${showName.toLowerCase()}/`,
    );
  });
});
