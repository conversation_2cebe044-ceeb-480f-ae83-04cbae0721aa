import {
  getShowPageHeroLifecycleByStatus,
  getHeroInfoBarByStatus,
} from './status-lifecycle-mapper'
import { isDateInThePast } from './event-helpers/is-date-in-the-past'

jest.mock('dayjs', () => {
  const actualDayjs = jest.requireActual('dayjs')
  actualDayjs.utc = jest.fn(() => actualDayjs('2024-06-01T10:00:00Z'))
  actualDayjs.prototype.tz = function () {
    return this
  }
  actualDayjs.prototype.format = function () {
    return 'Sat, Jun 1, 2024 @ 10:00 AM'
  }
  return actualDayjs
})

jest.mock('./event-helpers/is-date-in-the-past', () => ({
  isDateInThePast: jest.fn(),
}))

describe('getShowPageHeroLifecycleByStatus', () => {
  it('returns mapped label for known status', () => {
    expect(getShowPageHeroLifecycleByStatus({ status: 'sold-out' })).toBe('Sold Out')
  })

  it('returns presale date for teaser status with preSales', () => {
    const preSales = [
      {
        description: 'Presale',
        iso: '2024-06-01T10:00:00Z',
        timezone: 'Europe/Athens',
      },
    ]
    expect(getShowPageHeroLifecycleByStatus({ status: 'teaser', preSales })).toBe(
      'Presale opens Sat, Jun 1, 2024 @ 10:00 AM'
    )
  })

  it('returns undefined for teaser status without preSales', () => {
    expect(getShowPageHeroLifecycleByStatus({ status: 'teaser' })).toBeUndefined()
  })

  it('returns "Presales open" for pre-sale status with preSales', () => {
    const preSales = [
      {
        description: 'Presale',
        iso: '2024-06-01T10:00:00Z',
        timezone: 'Europe/Athens',
      },
    ]
    expect(getShowPageHeroLifecycleByStatus({ status: 'pre-sale', preSales })).toBe(
      'Presales open'
    )
  })

  it('returns undefined for pre-sale status without preSales', () => {
    expect(getShowPageHeroLifecycleByStatus({ status: 'pre-sale' })).toBeUndefined()
  })

  it('returns undefined for unknown status', () => {
    expect(getShowPageHeroLifecycleByStatus({ status: 'unknown' })).toBeUndefined()
  })
})

describe('getHeroInfoBarByStatus', () => {
  afterEach(() => {
    jest.clearAllMocks()
  })

  it('returns undefined for free, private, closed statuses', () => {
    ;(isDateInThePast as jest.Mock).mockImplementation(() => false)
    expect(getHeroInfoBarByStatus({ status: 'free' })).toBeUndefined()
    expect(getHeroInfoBarByStatus({ status: 'private' })).toBeUndefined()
    expect(getHeroInfoBarByStatus({ status: 'closed' })).toBeUndefined()
  })

  it('returns undefined if generalOnSale is missing', () => {
    ;(isDateInThePast as jest.Mock).mockImplementation(() => false)
    expect(getHeroInfoBarByStatus({ status: 'coming-soon' })).toBeUndefined()
  })

  it('returns undefined if date is in the past', () => {
    ;(isDateInThePast as jest.Mock).mockImplementation(() => true)
    expect(
      getHeroInfoBarByStatus({
        status: 'coming-soon',
        generalOnSale: {
          iso: '2024-06-01T10:00:00Z',
          timezone: 'Europe/Athens',
        },
      })
    ).toBeUndefined()
  })

  it('returns formatted general sale info if date is in the future', () => {
    ;(isDateInThePast as jest.Mock).mockImplementation(() => false)
    expect(
      getHeroInfoBarByStatus({
        status: 'coming-soon',
        generalOnSale: {
          iso: '2024-06-01T10:00:00Z',
          timezone: 'Europe/Athens',
        },
      })
    ).toBe('General sale Sat, Jun 1, 2024 @ 10:00 AM')
  })
})
