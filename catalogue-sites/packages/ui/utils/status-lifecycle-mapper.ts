import dayjs from 'dayjs'

import { isDateInThePast } from 'utils/event-helpers/is-date-in-the-past'

// This is a map of the common lifecycle translations for the hero components of the Tour and Show pages
export const lifecycleMap = {
  'coming-soon': 'Coming Soon',
  'sold-out': 'Sold Out',
  'postponed': 'Postponed',
  'cancelled': 'Canceled',
  'free': 'Free',
  'private': 'Private',
  'closed': 'Closed',
}

type PreSale = {
  description: string
  hideDateTime?: boolean
  iso: string
  soldOut?: boolean
  timezone: string
}

export const getShowPageHeroLifecycleByStatus = ({
  status,
  preSales,
}: {
  status: string
  preSales?: PreSale[]
}) => {
  if (lifecycleMap[status]) {
    return lifecycleMap[status]
  }

  if (status === 'teaser') {
    const firstPreSale = preSales?.[0] || undefined
    if (firstPreSale) {
      return `Presale opens ${dayjs
        .utc(firstPreSale.iso)
        .tz(firstPreSale.timezone)
        .format('ddd, MMM D, YYYY @ h:mm A')}`
    }

    return undefined
  }

  if (status === 'pre-sale') {
    const preSalesLength = preSales?.length || 0
    if (preSalesLength > 0) {
      return 'Presales open'
    }
    return undefined
  }

  return undefined
}

const noInfoBarStatuses = ['free', 'private', 'closed']

type OnSale = {
  iso: string
  timezone: string
}

export const getHeroInfoBarByStatus = ({
  status,
  generalOnSale,
}: {
  status: string
  generalOnSale?: OnSale
}) => {
  if (noInfoBarStatuses.includes(status)) {
    return undefined
  }

  if (!generalOnSale) {
    return undefined
  }

  if (isDateInThePast(generalOnSale.iso, generalOnSale.timezone)) {
    return undefined
  }

  return `General sale ${dayjs
    .utc(generalOnSale.iso)
    .tz(generalOnSale.timezone)
    .format('ddd, MMM D, YYYY @ h:mm A')}`
}

export const mapStatus = (status: string, date?: string) => {
  const mapStatusText = {
    'coming-soon': 'Coming soon',
    'teaser': date ? `On sale ${date}` : 'Tickets not yet released',
    'pre-sale': 'Pre-sale',
    'sold-out': 'Sold out',
    'postponed': 'Postponed',
    'cancelled': 'Canceled',
    'free': 'Free',
    'private': 'Private',
  }

  return mapStatusText[status] || ''
}

export const getTourPageHeroLifecycleByStatus = ({
  status,
}: {
  status: string
}) => {
  if (lifecycleMap[status]) {
    return lifecycleMap[status]
  }

  if (status === 'teaser') {
    return lifecycleMap['coming-soon']
  }

  return undefined
}
