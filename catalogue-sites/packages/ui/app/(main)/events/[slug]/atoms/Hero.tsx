import React from 'react'
import {
  HeroGallery,
  type MediaProps,
  Stack,
  Typography,
  Box,
  Button,
} from '@atg-digital/ui-components'
import { useTheme } from '@mui/material'

import { emitGeneralEvent } from 'lib/dataLayer/general'

type Content = {
  iconText?: string
  title: string
  subtitle?: string
  lifecycle?: string
  lifecycleSubtitle?: string
  description?: string
  cta?: {
    label: string
    href: string
  }
  infoBarContent?: string
  icon?: React.ReactNode
}

export type HeroProps = {
  heroImage: { [key: number]: string }
  videoUrl?: string
  imageGallery?: {
    src: string
    alt: string
    srcSets: {
      gallery: MediaProps['imageSrc']
      carousel: string[]
    }
  }[]
} & Content

const Content = ({
  iconText,
  icon,
  description,
  title,
  subtitle,
  lifecycle,
  lifecycleSubtitle,
  cta,
}: Content) => {
  const { tokens } = useTheme()
  return (
    <Stack
      p={{
        xs: `${tokens.system_spacing_m} ${tokens.system_spacing_s} ${tokens.system_spacing_l} ${tokens.system_spacing_s}`,
        lg: `${tokens.system_spacing_m} ${tokens.system_spacing_m} ${tokens.system_spacing_l} ${tokens.system_spacing_l}`,
        xl: `${tokens.system_spacing_m} ${tokens.system_spacing_m} ${tokens.system_spacing_l} ${tokens.system_spacing_xxl}`,
      }}
      spacing={{ xs: tokens.system_spacing_m, lg: 'auto' }}
      height="100%"
      justifyContent={{ xs: 'flex-start', lg: 'space-between' }}
      bgcolor={tokens.foundations_palette_deepPurple_50}
    >
      <Stack spacing={tokens.system_spacing_xxs}>
        <Stack
          sx={{ display: { xs: 'block', lg: 'none' } }}
          spacing={tokens.system_spacing_xxs}
        >
          <Typography variant="titleLarge">{title}</Typography>
          {subtitle && <Typography variant="labelLarge">{subtitle}</Typography>}
        </Stack>
        <Stack
          sx={{ display: { xs: 'none', lg: 'block' } }}
          spacing={tokens.system_spacing_xxs}
        >
          <Typography variant="titleSmall">{title}</Typography>
          {subtitle && <Typography variant="labelSmall">{subtitle}</Typography>}
        </Stack>
        {iconText && (
          <Box display="flex" gap={1}>
            {icon}
            <Typography variant="bodySmall">{iconText}</Typography>
          </Box>
        )}
        <Typography variant="bodySmall">{description}</Typography>
      </Stack>
      <Stack>
        <Stack display={{ xs: 'flex', lg: 'none' }}>
          {lifecycle && (
            <Typography variant="titleSmall">{lifecycle}</Typography>
          )}
          {lifecycleSubtitle && (
            <Typography variant="labelLarge">{lifecycleSubtitle}</Typography>
          )}
        </Stack>
        <Stack display={{ xs: 'none', lg: 'flex' }}>
          {lifecycle && (
            <Typography variant="labelLarge">{lifecycle}</Typography>
          )}
          {lifecycleSubtitle && (
            <Typography variant="labelSmall">{lifecycleSubtitle}</Typography>
          )}
        </Stack>
        {cta?.label && (
          <>
            <Box display={{ xs: 'block', lg: 'none' }}>
              <Button
                variant="primary"
                href={cta.href}
                size="medium"
                sx={{
                  mt: 2,
                }}
                fullWidth
                onClick={() =>
                  emitGeneralEvent('onClickShowBuyTicketText', cta.href || '/')
                }
              >
                {cta.label}
              </Button>
            </Box>
            <Box display={{ xs: 'none', lg: 'block' }}>
              <Button
                variant="primary"
                href={cta.href}
                size="medium"
                sx={{
                  mt: 2,
                }}
                fullWidth={false}
                onClick={() =>
                  emitGeneralEvent('onClickShowBuyTicketText', cta.href || '/')
                }
              >
                {cta.label}
              </Button>
            </Box>
          </>
        )}
      </Stack>
    </Stack>
  )
}

const InfoBarContent = ({ infoBarContent }: { infoBarContent: string }) => {
  return (
    <Box padding={1} component="span">
      <Typography variant="labelLarge" component="span">
        {infoBarContent}
      </Typography>
    </Box>
  )
}

export const Hero = ({
  heroImage,
  videoUrl,
  imageGallery = [],
  iconText,
  icon,
  description,
  title,
  subtitle,
  lifecycle,
  lifecycleSubtitle,
  cta,
  infoBarContent,
}: HeroProps) => {
  return (
    <HeroGallery
      heroImage={heroImage}
      videoUrl={videoUrl}
      imageGallery={imageGallery}
      onPlayButtonClicked={() =>
        emitGeneralEvent('onClickPlayTrailer', videoUrl || '')
      }
      content={
        <Content
          iconText={iconText}
          icon={icon}
          title={title}
          subtitle={subtitle}
          lifecycle={lifecycle}
          description={description}
          lifecycleSubtitle={lifecycleSubtitle}
          cta={cta}
        />
      }
      secondaryInfo={
        infoBarContent ? (
          <InfoBarContent infoBarContent={infoBarContent} />
        ) : undefined
      }
    />
  )
}
