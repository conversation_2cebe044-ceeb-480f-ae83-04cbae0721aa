'use client'

import {
  Grid,
  Box,
  type BreadcrumbsProps,
  InfoBar,
  IndexLinks,
  InfoSection,
  Stack,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  RichTextParser,
  RichTextParserInline,
  SectionTitle,
  Link,
  Quotes,
  type InfoItemProps,
  type SlideData,
  type MediaProps,
  ContentCards,
} from '@atg-digital/ui-components'
import { SITE_HEADER_HEIGHT } from '@atg-digital/ui-navigation'
import { KeyboardArrowDownIcon, LocationOnIcon } from '@atg-digital/ui-icons'
import { useMediaQuery, useTheme } from '@mui/material'
import { t } from 'i18next'
import React, { useRef, useEffect, useState, useMemo } from 'react'

import type { CastItem, VenueAccessibility, PreSale, Tickets } from './types'
import { CastSection } from './atoms/CastSection'
import { DetailsSection } from './atoms/DetailsSection'
import { UpcomingPerformances } from './atoms/UpcomingPerformances'
import { AccessibilitySection } from './atoms/AccessibilitySection'
import { TicketsSection } from './atoms/TicketsSection'
import { Hero } from './atoms/Hero'
import { GridContainer } from './atoms/GridContainer'
import { getCloudinaryImage } from './cloudinary-utils'

import { VenueInfo } from 'components/BusinessSchema'
import { BreadcrumbsWithSchema } from 'components/BreadcrumbsWithSchema'
import { Upsell, type UpsellProps } from 'components/Upsell'
import { useHasScrolledPast } from 'hooks/use-has-scrolled-past'
import { emitGeneralEvent } from 'lib/dataLayer/general'

const allBrowseLinks = {
  details: { label: 'Details', href: 'details' },
  planYourVisit: { label: 'Plan your visit', href: 'plan-your-visit' },
  castAndCreatives: { label: 'Cast & creatives', href: 'cast-and-creatives' },
  soundtrack: { label: 'Soundtrack', href: 'soundtrack' },
  criticReviews: { label: 'Critic reviews', href: 'critic-reviews' },
  access: { label: 'Accessibility', href: 'access' },
  faq: { label: 'FAQ', href: 'faq' },
}
const ESTIMATED_FOOTER_HEIGHT_LG = 500
const ESTIMATED_FOOTER_HEIGHT_XS = 0
const TERMS_AND_CONDITIONS_URL = '/legal/terms-and-conditions/'
const commonLinksAttributes = {
  offsetId: 'index-links-sticky-wrapper',
  active: false,
}

type AggregatorSiteShowPageProps = {
  eventName: string
  venueInfo?: VenueInfo
  venueName?: string
  venueDistrict?: string
  runningTime?: string
  genres?: string[]
  schedule?: string
  description?: string
  inventorySlug: string
  websiteSlug: string
  breadcrumbs?: BreadcrumbsProps['breadcrumbs']
  resolvedInfoBar?: string[]
  faqs?: {
    body?: string
    title?: string
  }[]
  castList?: CastItem[] | null
  upsell?: UpsellProps
  soundtrack?: string
  goodToKnowInfoItems?: InfoItemProps[]
  goodToKnowLink?: {
    text: string
    url: string
  }
  reviews?: SlideData[]
  venueAccessibility?: VenueAccessibility
  upcomingPerformances?: {
    date: {
      iso: string
      timezone: string
    }
    id: string
  }[]
  accessPerformances?: {
    type: string
    date: string
    sourceId: string
  }[]
  planYourVisit?: {
    imageData: {
      src: string
      alt: string
    }
    text: {
      description?: string
    }
    linksVariant: 'text' | 'button'
    links: {
      linkText: string
      link: string
      openInNewTab: boolean
    }[]
  }
  venueAddress?: {
    streetAddress?: string
    city?: string
    state?: string
    postalCode?: string
    country?: string
  }
  preSales?: PreSale[]
  tickets?: Tickets
  hero: {
    heroImage: { [key: number]: string }
    videoUrl?: string
    description?: string
    imageGallery?: {
      src: string
      alt: string
      srcSets: {
        gallery: MediaProps['imageSrc']
        carousel: string[]
      }
    }[]
    location?: string
    cta?: {
      label: string
      href: string
    }
    lifecycle?: string
    lifecycleSubtitle?: string
    infoBarContent?: string
  }
  buyTicketsCta?: {
    label: string
    href: string
  }
  showSidebarCta: boolean
}

export const AggregatorSiteShowPage = ({
  eventName,
  websiteSlug,
  inventorySlug,
  venueInfo,
  venueName = '',
  venueDistrict = '',
  runningTime,
  genres,
  schedule,
  description,
  breadcrumbs,
  resolvedInfoBar,
  faqs = [],
  castList = [],
  upsell,
  soundtrack,
  goodToKnowInfoItems = [],
  goodToKnowLink,
  reviews = [],
  upcomingPerformances = [],
  accessPerformances = [],
  planYourVisit,
  venueAddress,
  venueAccessibility,
  preSales = [],
  tickets = {},
  hero,
  buyTicketsCta,
  showSidebarCta = false,
}: AggregatorSiteShowPageProps) => {
  const { tokens, breakpoints } = useTheme()
  const [pageHasMounted, setPageHasMounted] = useState(false)

  const hasPlanYourVisitSection = planYourVisit !== undefined
  const hasCastAndCreativesSection = castList && castList.length > 0
  const hasSoundtrackSection = Boolean(soundtrack)
  const hasReviewsSection = reviews.length > 0
  const hasUpcomingPerformancesSection = upcomingPerformances.length > 0
  const hasVenueAccessFeatures = Boolean(venueAccessibility?.features?.length)
  const hasAccessSection =
    accessPerformances.length > 0 || hasVenueAccessFeatures
  const hasFaqSection = faqs.length > 0

  const visibleLinks = useMemo(() => {
    const links = [
      {
        ...allBrowseLinks.details,
        href: `#${allBrowseLinks.details.href}`,
        ...commonLinksAttributes,
        active: false,
      },
    ]

    if (hasPlanYourVisitSection) {
      links.push({
        ...allBrowseLinks.planYourVisit,
        href: `#${allBrowseLinks.planYourVisit.href}`,
        ...commonLinksAttributes,
      })
    }

    if (hasCastAndCreativesSection) {
      links.push({
        ...allBrowseLinks.castAndCreatives,
        href: `#${allBrowseLinks.castAndCreatives.href}`,
        ...commonLinksAttributes,
      })
    }

    if (hasSoundtrackSection) {
      links.push({
        ...allBrowseLinks.soundtrack,
        href: `#${allBrowseLinks.soundtrack.href}`,
        ...commonLinksAttributes,
      })
    }

    if (hasReviewsSection) {
      links.push({
        ...allBrowseLinks.criticReviews,
        href: `#${allBrowseLinks.criticReviews.href}`,
        ...commonLinksAttributes,
      })
    }

    if (hasAccessSection) {
      links.push({
        ...allBrowseLinks.access,
        href: `#${allBrowseLinks.access.href}`,
        ...commonLinksAttributes,
      })
    }

    if (hasFaqSection) {
      links.push({
        ...allBrowseLinks.faq,
        href: `#${allBrowseLinks.faq.href}`,
        ...commonLinksAttributes,
      })
    }

    return links
  }, [
    hasPlanYourVisitSection,
    hasCastAndCreativesSection,
    hasSoundtrackSection,
    hasReviewsSection,
    hasAccessSection,
    hasFaqSection,
  ])

  const [browseLinks, setBrowseLinks] = useState(visibleLinks)

  const isLgUp = useMediaQuery(breakpoints.up('lg'))
  const offsetInPX = isLgUp ? SITE_HEADER_HEIGHT.lg : SITE_HEADER_HEIGHT.xs
  const offset = Number(offsetInPX.split('px')[0])
  const browseLinksOffset = isLgUp ? offsetInPX : '0px'
  const estimatedFooterHeight = isLgUp
    ? ESTIMATED_FOOTER_HEIGHT_LG
    : ESTIMATED_FOOTER_HEIGHT_XS

  const indexLinksRef = useRef<HTMLDivElement | null>(null)
  const buyTicketMobileButtonRef = useRef<HTMLButtonElement | null>(null)
  const buyTicketDesktopButtonRef = useRef<HTMLButtonElement | null>(null)

  const sectionRefs = useMemo(
    () =>
      Object.keys(allBrowseLinks).reduce(
        (acc, key) => {
          acc[key] = React.createRef<HTMLDivElement>()
          return acc
        },
        {} as Record<
          keyof typeof allBrowseLinks,
          React.RefObject<HTMLDivElement>
        >
      ),
    []
  )

  const browseLinksWithGA = useMemo(
    () =>
      browseLinks.map((link) => ({
        ...link,
        onClick: () => emitGeneralEvent('onClickShowTab', link.label),
      })),
    [browseLinks]
  )

  const showStickyHeader = useHasScrolledPast(indexLinksRef, {
    topOffset: offset,
  })

  const showMobileStickyBuyTicketButton = useHasScrolledPast(
    buyTicketMobileButtonRef,
    {
      topOffset: offset,
    }
  )

  const showDesktopStickyBuyTicketButton = useHasScrolledPast(
    buyTicketDesktopButtonRef,
    {
      topOffset: offset,
    }
  )

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const visible = entries
          .filter((entry) => entry.isIntersecting)
          .sort((a, b) => b.intersectionRatio - a.intersectionRatio)

        if (visible.length > 0) {
          const mostVisibleId = visible[0].target.id

          setBrowseLinks((prevLinks) =>
            prevLinks.map((link) => ({
              ...link,
              active: link.href.slice(1) === mostVisibleId,
            }))
          )
        } else {
          setBrowseLinks((prevLinks) =>
            prevLinks.map((link) => ({
              ...link,
              active: false,
            }))
          )
        }
      },
      {
        rootMargin: `-${browseLinksOffset} 0px -${estimatedFooterHeight}px 0px`, // the footer height helps with the last section
        threshold: [0.25, 0.5, 0.75],
      }
    )

    Object.values(sectionRefs).forEach((ref) => {
      if (ref.current) observer.observe(ref.current)
    })

    return () => observer.disconnect()
  }, [browseLinksOffset, estimatedFooterHeight, sectionRefs, browseLinks])

  useEffect(() => {
    setPageHasMounted(true)
  }, [])

  const stickyWrapperSx = {
    backgroundColor: showStickyHeader
      ? tokens.foundations_palette_grey_100
      : tokens.foundations_palette_common_white,
    position: 'sticky',
    top: offset,
    left: 0,
    right: 0,
    zIndex: 2,
    paddingY: tokens.foundations_styles_spacingScaleNumeric_5,
    paddingX: tokens.foundations_styles_spacingScaleNumeric_5,
  }

  // we do this to avoid re-rendering the RichTextParser unnecessarily
  const parsedFaqs = useMemo(
    () =>
      faqs.map((faq) => (
        <RichTextParser
          key={faq.title}
          richText={faq.body || ''}
          imageTransformFunction={getCloudinaryImage}
        />
      )),
    [faqs]
  )

  // this is usually going to be a spotify iframe
  // we try to avoid re-rendering as much as possible and only render this client side
  // as spotify will return 429 if too many requests are made
  const soundtrackBody = useMemo(
    () => (
      <RichTextParser
        richText={soundtrack || ''}
        imageTransformFunction={getCloudinaryImage}
      />
    ),
    [soundtrack]
  )

  const hasGoodToKnowSection = goodToKnowInfoItems.length > 0
  const goodToKnowTermsConditionsLink = goodToKnowLink ? (
    <Link href={goodToKnowLink.url} target="_blank">
      {goodToKnowLink.text}
    </Link>
  ) : (
    <Link href={TERMS_AND_CONDITIONS_URL} target="_blank">
      {t('aggregatorSiteShowPage.showDetails.goodToKnow.tAndCsLinkTitle')}
    </Link>
  )
  const venueAddressText = venueAddress
    ? `${venueAddress?.streetAddress}, ${venueAddress?.city}, ${venueAddress?.state}, ${venueAddress?.postalCode}, ${venueAddress?.country}`
    : undefined

  return (
    <>
      {breadcrumbs && <BreadcrumbsWithSchema breadcrumbs={breadcrumbs} />}
      {resolvedInfoBar && <InfoBar>{resolvedInfoBar}</InfoBar>}

      <Hero
        heroImage={hero.heroImage}
        description={hero.description}
        icon={<LocationOnIcon />}
        iconText={hero.location}
        videoUrl={hero.videoUrl}
        imageGallery={hero.imageGallery}
        title={eventName}
        lifecycle={hero.lifecycle}
        lifecycleSubtitle={hero.lifecycleSubtitle}
        cta={hero.cta}
        infoBarContent={hero.infoBarContent}
      />

      {hero.cta?.href && <Box ref={buyTicketMobileButtonRef} />}

      <Box ref={indexLinksRef} />
      <Box sx={stickyWrapperSx} id="index-links-sticky-wrapper">
        <GridContainer>
          <Grid size={{ xs: 11, md: 9, lg: 10, xl: 8 }} position="relative">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Box width={{ xs: '100%', lg: 'auto' }}>
                <IndexLinks
                  linksList={browseLinksWithGA}
                  linksTitle={t('aggregatorSiteShowPage.indexLinks.title')}
                />
              </Box>
              {showDesktopStickyBuyTicketButton && showSidebarCta && (
                <Box display={{ xs: 'none', lg: 'block' }}>
                  <Button
                    variant="primary"
                    size="small"
                    href={buyTicketsCta?.href || '/'}
                    onClick={() =>
                      emitGeneralEvent(
                        'onClickShowBuyTicketText',
                        buyTicketsCta?.href || '/'
                      )
                    }
                  >
                    {buyTicketsCta?.label ||
                      t('aggregatorSiteShowPage.buyTicketsButton.label')}
                  </Button>
                </Box>
              )}
            </Box>
          </Grid>
        </GridContainer>
      </Box>
      <GridContainer>
        <Grid size={{ xs: 11, md: 9, lg: 10, xl: 8 }} position="relative">
          <Stack
            width="100%"
            display="grid"
            gridTemplateColumns={{ xs: 'auto', lg: '2fr 1fr' }}
            gridTemplateRows="auto"
            gridTemplateAreas={{ xs: '"main"', lg: '"main sidebar"' }}
            columnGap={5}
            pb={{ xs: 2, lg: 7 }}
          >
            <Stack gridArea="main" gap={{ xs: 5, lg: 7 }} overflow="hidden">
              <Stack id={allBrowseLinks.details.href} ref={sectionRefs.details}>
                <DetailsSection
                  inventorySlug={inventorySlug}
                  venueName={venueName}
                  venueSlug={venueInfo?.venueSlug || ''}
                  venueDistrict={venueDistrict}
                  runningTime={runningTime}
                  genres={genres}
                  schedule={schedule}
                  description={description}
                  preSales={preSales}
                />

                <Stack pt={4} gap={2}>
                  {hasGoodToKnowSection && (
                    <Stack gap={2}>
                      <Typography variant="titleSmall" component="p">
                        {t(
                          'aggregatorSiteShowPage.showDetails.goodToKnow.title'
                        )}
                      </Typography>

                      <InfoSection
                        isList
                        infoItems={goodToKnowInfoItems.map(
                          ({ text, icon, title, link }) => {
                            const textContainsHtml =
                              text && /<[a-z][\s\S]*>/i.test(text)

                            return {
                              icon,
                              title,
                              text: (textContainsHtml
                                ? null
                                : text) as InfoItemProps['text'],
                              children: textContainsHtml ? (
                                <RichTextParserInline
                                  richText={text as string}
                                />
                              ) : null,
                              link,
                            }
                          }
                        )}
                      />
                    </Stack>
                  )}

                  <Typography variant="bodySmall" component="p">
                    {goodToKnowTermsConditionsLink}{' '}
                    {t(
                      'aggregatorSiteShowPage.showDetails.goodToKnow.tAndCsCopy'
                    )}
                  </Typography>
                </Stack>

                {Object.keys(tickets).length > 0 && (
                  <Box display={{ xs: 'block', lg: 'none' }} mt={5}>
                    <TicketsSection tickets={tickets} />
                  </Box>
                )}
              </Stack>
              {hasPlanYourVisitSection && (
                <Stack
                  id={allBrowseLinks.planYourVisit.href}
                  ref={sectionRefs.planYourVisit}
                  rowGap={2}
                >
                  <SectionTitle
                    title={t('aggregatorSiteShowPage.planYourVisit.title')}
                  />
                  <Box>
                    <Typography variant="labelSmall" component="p">
                      {venueName}
                    </Typography>
                    {venueAddressText && (
                      <Typography variant="labelSmall" component="p">
                        {venueAddressText}
                      </Typography>
                    )}
                  </Box>
                  <ContentCards
                    contentCards={[planYourVisit]}
                    variant="horizontal"
                  />
                </Stack>
              )}
              {hasCastAndCreativesSection && (
                <Stack
                  id={allBrowseLinks.castAndCreatives.href}
                  ref={sectionRefs.castAndCreatives}
                >
                  <CastSection cast={castList} />
                </Stack>
              )}
              {hasSoundtrackSection && (
                <Stack
                  id={allBrowseLinks.soundtrack.href}
                  ref={sectionRefs.soundtrack}
                  rowGap={2}
                  onClick={() =>
                    emitGeneralEvent('onClickPlaySoundtrack', websiteSlug)
                  }
                >
                  <SectionTitle
                    title={t('aggregatorSiteShowPage.soundtrack.title')}
                  />
                  {pageHasMounted ? soundtrackBody : undefined}
                </Stack>
              )}

              {hasReviewsSection && (
                <Stack
                  id={allBrowseLinks.criticReviews.href}
                  ref={sectionRefs.criticReviews}
                >
                  <Quotes
                    slideData={reviews}
                    title={t('aggregatorSiteShowPage.criticReviews.title')}
                    showDividers={false}
                  />
                </Stack>
              )}
              <Box display={{ xs: 'block', lg: 'none' }}>
                {hasUpcomingPerformancesSection && (
                  <Stack rowGap={2}>
                    <UpcomingPerformances
                      websiteSlug={websiteSlug}
                      performancesData={upcomingPerformances}
                    />
                  </Stack>
                )}
              </Box>
              {hasAccessSection && (
                <Stack id={allBrowseLinks.access.href} ref={sectionRefs.access}>
                  <AccessibilitySection
                    accessPerformances={accessPerformances}
                    websiteSlug={websiteSlug}
                    venueSlug={venueInfo?.venueSlug || ''}
                    venueAccessibility={venueAccessibility}
                  />
                </Stack>
              )}
              {upsell && (
                <Box display={{ xs: 'block', lg: 'none' }}>
                  <Box display={{ xs: 'block', sm: 'none' }}>
                    <Upsell {...upsell} />
                  </Box>
                  <Box display={{ xs: 'none', sm: 'block' }}>
                    <Upsell {...upsell} direction="horizontal" />
                  </Box>
                </Box>
              )}
              {hasFaqSection && (
                <Stack
                  id={allBrowseLinks.faq.href}
                  ref={sectionRefs.faq}
                  rowGap={2}
                >
                  <SectionTitle
                    title={t('aggregatorSiteShowPage.faqs.title')}
                  />
                  <Stack>
                    {faqs.map((faq, index) => (
                      <Accordion
                        onClick={() =>
                          emitGeneralEvent('onClickFaqExpand', faq.title || '')
                        }
                        key={index}
                        slotProps={{ transition: { unmountOnExit: false } }}
                      >
                        <AccordionSummary
                          aria-controls={`accordion-details-content-${index}`}
                          id={`faqs-${index}`}
                          expandIcon={
                            <KeyboardArrowDownIcon
                              color={tokens.components_accordion_fg}
                            />
                          }
                        >
                          <Typography component="span" variant="labelSmall">
                            {faq.title}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>{parsedFaqs[index]}</AccordionDetails>
                      </Accordion>
                    ))}
                  </Stack>
                </Stack>
              )}
            </Stack>
            <Stack
              gridArea="sidebar"
              display={{ xs: 'none', lg: 'flex' }}
              gap={7}
            >
              {showSidebarCta && (
                <Stack rowGap={2}>
                  <TicketsSection tickets={tickets} />
                  <Button
                    variant="primary"
                    size="large"
                    href={buyTicketsCta?.href || '/'}
                    fullWidth={false}
                    onClick={() =>
                      emitGeneralEvent(
                        'onClickShowBuyTicketText',
                        buyTicketsCta?.href || '/'
                      )
                    }
                  >
                    {buyTicketsCta?.label ||
                      t('aggregatorSiteShowPage.buyTicketsButton.label')}
                  </Button>

                  <Box ref={buyTicketDesktopButtonRef} />
                </Stack>
              )}
              {hasUpcomingPerformancesSection && (
                <Stack rowGap={2}>
                  <UpcomingPerformances
                    websiteSlug={websiteSlug}
                    performancesData={upcomingPerformances}
                  />
                </Stack>
              )}
              {upsell && <Upsell {...upsell} />}
            </Stack>
          </Stack>
        </Grid>
      </GridContainer>
      {showMobileStickyBuyTicketButton && (
        <Stack
          display={{ xs: 'flex', lg: 'none' }}
          justifyContent="center"
          alignItems="center"
          position="sticky"
          bottom={0}
          width="100%"
          bgcolor={tokens.foundations_palette_common_white}
          gap={tokens.foundations_styles_spacingScale_3}
          padding={tokens.foundations_styles_spacingScale_5}
          borderTop={`1px solid ${tokens.foundations_palette_grey_300}`}
          zIndex={2}
        >
          <Button
            variant="primary"
            size="large"
            href={hero.cta?.href || '/'}
            onClick={() =>
              emitGeneralEvent(
                'onClickShowBuyTicketText',
                hero.cta?.href || '/'
              )
            }
          >
            {hero.cta?.label ||
              t('aggregatorSiteShowPage.buyTicketsButton.label')}
          </Button>
        </Stack>
      )}
    </>
  )
}
