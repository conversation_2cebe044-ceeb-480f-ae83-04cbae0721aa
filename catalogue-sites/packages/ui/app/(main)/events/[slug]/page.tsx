import * as React from 'react'
import { Metadata } from 'next'
import { draftMode } from 'next/headers'
import { notFound } from 'next/navigation'
import { t } from 'i18next'

import { BlockPage } from '../../BlockPage'

import { EventPage } from './EventPage'
import { AggregatorSiteTourPage } from './AggregatorSiteTourPage'
import { getFilteredOnSaleDates } from './get-filtered-onsale-dates'

import { getSiteSettings } from 'data/get-site-settings'
import { fetchPreviewContent, UmbracoLocale } from 'data/preview-client'
import { getPageContent } from 'data/get-page-content'
import { getTourContent } from 'data/get-tour-content'
import { NotFoundError } from 'lib/errors'
import { getPathFromSlug } from 'utils/site-url-helper'
import { getBreadcrumbs } from 'utils/get-breadcrumbs'
import { getPageInfoBar } from 'utils/get-page-info-bar'
import { isDateInThePast } from 'utils/event-helpers/is-date-in-the-past'
import { getSeoFields, getShareImage } from 'utils/get-seo-fields'
import { brandAlias, isAtgBrand } from 'utils/site/brand-alias'
import { getTypesenseTourContent } from 'data/typesense/get-typesense-tour-content'
import { getAggregatorSiteHeroCloudinaryImages } from 'utils/cloudinary-url'
import {
  getCloudinaryImageUrlAutoQualityAndFormat,
  getCloudinaryMediaGallerySrcSet,
} from 'utils/image-mapping'
import { getTourPageHeroLifecycleByStatus } from 'utils/status-lifecycle-mapper'

const statusesToShowHeroCta = [
  'teaser',
  'coming-soon',
  'pre-sale',
  'general-onsale',
]

async function EventItemRoute(props: { params: Promise<{ slug: string }> }) {
  const { slug } = await props.params

  const { isEnabled: isDraftMode } = await draftMode()

  try {
    const settings = await getSiteSettings()

    const pageUrl = getPathFromSlug(`/events/${slug}`)

    const locale: UmbracoLocale = settings.brandConfig.defaultLanguage
    const umbracoLocale = locale || 'en-US'

    const pageData = isDraftMode
      ? await fetchPreviewContent(pageUrl, umbracoLocale)
      : isAtgBrand
      ? await getTourContent(slug)
      : await getPageContent(pageUrl)

    const websiteSlug = pageData.websiteSlug

    const timeZone = settings.brandConfig.timeZone

    const breadcrumbs = await getBreadcrumbs({
      path: pageUrl,
      currentName: pageData.eventName,
      currentPageName: pageData.pageName,
      hideFromBreadcrumbs: pageData.hideFromBreadcrumbs,
    })

    const resolvedInfoBar = getPageInfoBar({
      pageDataInfoBars: pageData?.informationBars,
      settingsInfoBars: settings.infoBarContent,
    })

    if (pageData.contentTypeAlias === 'production') {
      const isEmbargoed =
        pageData.embargoDate && !isDateInThePast(pageData.embargoDate, timeZone)

      if (isEmbargoed) {
        throw new NotFoundError(`No content found for ${pageUrl}`)
      }

      return (
        <EventPage
          blocks={pageData.blocks}
          onSaleDatesFiltered={getFilteredOnSaleDates(pageData)}
          titleShot={pageData.titleShot}
          titleShotTall={pageData.titleShotTall}
          eventName={pageData.eventName}
          eventDescription={pageData.eventDescription}
          inventorySlug={pageData.showSlug}
          pricingDescription={pageData.pricingDescription}
          salesPeriod={pageData.salesPeriod}
          startTime={pageData.startTime}
          runningTime={pageData.runningTime}
          accessPerformances={pageData.accessPerformances}
          primaryCTA={pageData.primaryCTA}
          secondaryCTA={pageData.secondaryCTA}
          venueInfo={pageData.venueInfo}
          performanceTimes={pageData.performanceTimes}
          groupInformation={pageData.groupInformation}
          onSaleDates={pageData.onSaleDates}
          embargoDate={pageData.embargoDate}
          isSoldOut={pageData.isSoldOut}
          forcePostponedOrCanceled={pageData.forcePostponedOrCanceled}
          specialEventType={pageData.specialEventType}
          doorsOpen={pageData.doorsOpen}
          venue={pageData.venue}
          warningsAndGuidance={pageData.warningsAndGuidance}
          genre={pageData.genre}
          timeZone={timeZone}
          isSingleVenueSite={settings.isSingleVenueSite}
          pageUrl={slug}
          breadcrumbs={breadcrumbs}
          resolvedInfoBar={resolvedInfoBar}
          resellAvailable={pageData.resellAvailable}
          resellInformation={pageData.resellInformation}
          resellURL={pageData.resellURL}
        />
      )
    }

    if (pageData.contentTypeAlias === 'tour') {
      const result = await getTypesenseTourContent({
        websiteSlug,
        brandAlias,
      })

      const status = result?.status || 'closed'

      const getHeroCta = () => {
        if (
          statusesToShowHeroCta.includes(status) &&
          Boolean(result.show?.length)
        ) {
          return {
            label: t('aggregatorSiteShowPage.tourCta.label'),
            href: `#tour-venues`,
          }
        }

        if (status === 'sold-out' && pageData.resellAvailable) {
          return {
            label: t('aggregatorSiteShowPage.buyTicketsButton.label'),
            href: pageData.resellURL.url,
          }
        }
        return undefined
      }

      const seasonHero = {
        videoUrl: pageData.videoEmbed,
        description: pageData.shortTourDescription,
        heroImage: pageData.aggregatorHeroImage?._url
          ? getAggregatorSiteHeroCloudinaryImages({
              imageUrl: pageData.aggregatorHeroImage._url,
            })
          : '',
        imageGallery: pageData.productionGallery.map((image) => ({
          alt: image.media.altTag,
          src: getCloudinaryImageUrlAutoQualityAndFormat(image.src),
          srcSets: getCloudinaryMediaGallerySrcSet(image.src, 'Grid', true),
        })),
      }

      const tourHero = {
        ...seasonHero,
        lifecycle: getTourPageHeroLifecycleByStatus({
          status,
        }),
        lifecycleSubtitle:
          status === 'sold-out' && pageData.resellAvailable
            ? t('aggregatorSiteShowPage.hero.resaleText')
            : undefined,
        cta: getHeroCta(),
      }

      const upsell = pageData.productionUpsellTitle
        ? {
            title: pageData.productionUpsellTitle,
            headerText: pageData.productionUpsellHeaderText,
            imageSrc: pageData.productionUpsellImage._url,
            imageAlt: pageData.productionUpsellImage.altTag,
            bulletPoints: pageData.productionUpsellItems,
            cta: pageData.productionUpsellCta.name
              ? {
                  label: pageData.productionUpsellCta.name,
                  href: pageData.productionUpsellCta.url,
                }
              : undefined,
          }
        : undefined

      return (
        <AggregatorSiteTourPage
          eventName={pageData.eventName}
          resolvedInfoBar={resolvedInfoBar}
          websiteSlug={pageData.websiteSlug}
          breadcrumbs={breadcrumbs}
          tourShows={result?.show}
          faqs={
            pageData.productionFaqs?.map((faq) => ({ ...faq.content })) || []
          }
          castList={
            pageData.productionCastCrew?.map((item) => ({
              ...item.content,
            })) || []
          }
          soundtrack={pageData?.productionSoundtrack}
          genres={result?.genres || pageData.genreList || []}
          hero={pageData.isSeason ? seasonHero : tourHero}
          isSeason={pageData.isSeason}
          upsell={upsell}
        />
      )
    }

    return (
      <BlockPage
        blocks={pageData.blocks}
        timeZone={timeZone}
        pageUrl="events"
        breadcrumbs={breadcrumbs}
        resolvedInfoBar={resolvedInfoBar}
      />
    )
  } catch (error) {
    if (error instanceof NotFoundError) {
      return notFound()
    } else {
      console.error(`Error: ${error}`)
      throw error
    }
  }
}

export async function generateMetadata(props: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  try {
    const { slug } = await props.params
    if (isAtgBrand) {
      return {}
    }
    const pageUrl = getPathFromSlug(`/events/${slug}`)
    const pageData = await getPageContent(pageUrl)

    const seoFields = getSeoFields(pageData)

    const productionImage =
      pageData.openGraphImage ||
      pageData.productionShotTall ||
      pageData.productionShot

    return {
      ...seoFields,

      description: pageData.seoDescription || pageData.eventSubName,

      openGraph: {
        ...seoFields.openGraph,
        images: productionImage?._url
          ? [
              {
                url: getShareImage(productionImage?._url) || '',
                alt: productionImage?.altTag,
              },
            ]
          : undefined,
      },
    }
  } catch (e) {
    // failing gracefully - no need to throw an error
    return {}
  }
}

export default EventItemRoute
