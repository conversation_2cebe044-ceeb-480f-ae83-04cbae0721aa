import * as React from 'react'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { draftMode } from 'next/headers'
import { t } from 'i18next'

import { AggregatorSiteShowPage } from '../AggregatorSiteShowPage'

import { NotFoundError } from 'lib/errors'
import { isAtgBrand, brandAlias } from 'utils/site/brand-alias'
import { getPathFromSlug } from 'utils/site-url-helper'
import { getSiteSettings } from 'data/get-site-settings'
import { getTypesenseShowContent } from 'data/typesense/get-typesense-show-content'
import { getUpcomingShowPerformances } from 'data/typesense/get-upcoming-show-performances'
import { fetchPreviewContent, UmbracoLocale } from 'data/preview-client'
import { getPageContent } from 'data/get-page-content'
import { getBreadcrumbs } from 'utils/get-breadcrumbs'
import { getPageInfoBar } from 'utils/get-page-info-bar'
import {
  getCloudinaryUrl,
  getAggregatorSiteHeroCloudinaryImages,
} from 'utils/cloudinary-url'
import { getSeoFields, getShareImage } from 'utils/get-seo-fields'
import {
  getCloudinaryImageUrlAutoQualityAndFormat,
  getCloudinaryMediaGallerySrcSet,
} from 'utils/image-mapping'
import {
  getShowPageHeroLifecycleByStatus,
  getHeroInfoBarByStatus,
} from 'utils/status-lifecycle-mapper'

type Props = {
  venue: string
  slug: string
}

type PerformanceType = {
  id: string
  startDate: {
    iso: string
    timezone: string
  }
}[]

async function ShowPageRoute(props: { params: Promise<Props> }) {
  if (!isAtgBrand) {
    return notFound()
  }

  const params = await props.params

  const { slug, venue } = params
  const { isEnabled: isDraftMode } = await draftMode()

  try {
    const settings = await getSiteSettings()

    const pageUrl = getPathFromSlug(`/events/${slug}`, venue)
    const locale: UmbracoLocale = settings.brandConfig.defaultLanguage
    const umbracoLocale = locale || 'en-US'

    const pageData = isDraftMode
      ? await fetchPreviewContent(pageUrl, umbracoLocale)
      : await getPageContent(pageUrl)

    const websiteSlug = pageData.websiteSlug
    const inventorySlug = pageData.showSlug

    const breadcrumbs = await getBreadcrumbs({
      path: pageUrl,
      currentName: pageData.eventName,
      currentPageName: pageData.pageName,
      hideFromBreadcrumbs: pageData.hideFromBreadcrumbs,
    })

    const resolvedInfoBar = getPageInfoBar({
      pageDataInfoBars: pageData?.informationBars,
      settingsInfoBars: settings.infoBarContent,
    })

    const result = await getTypesenseShowContent({
      websiteSlug,
      venue: pageData.venueInfo.venueSlug,
      brandAlias,
    })

    const showSideBar = result?.status === 'general-onsale'

    const performancesRes = showSideBar
      ? await getUpcomingShowPerformances({
          websiteSlug,
          venue: pageData.venueInfo.venueSlug,
          brandAlias,
        })
      : []

    const upsell = pageData.productionUpsellTitle
      ? {
          title: pageData.productionUpsellTitle,
          headerText: pageData.productionUpsellHeaderText,
          imageSrc: pageData.productionUpsellImage._url,
          imageAlt: pageData.productionUpsellImage.altTag,
          bulletPoints: pageData.productionUpsellItems,
          cta: pageData.productionUpsellCta.name
            ? {
                label: pageData.productionUpsellCta.name,
                href: pageData.productionUpsellCta.url,
              }
            : undefined,
        }
      : undefined

    const goodToKnow =
      pageData.productionGoodToKnow?.map(({ content }) => ({
        title: content?.infoItemTitle,
        text: content?.infoItemText,
        icon: content?.infoItemIcon,
        link: content?.infoItemLink?.name
          ? {
              text: content?.infoItemLink?.name,
              url: content?.infoItemLink?.url,
            }
          : undefined,
      })) || []

    const goodToKnowLink = pageData.productionGoodToKnowLink?.name
      ? {
          text: pageData.productionGoodToKnowLink.name,
          url: pageData.productionGoodToKnowLink.url,
        }
      : undefined

    const reviews =
      pageData.productionReviews?.map(
        ({ content: { rating, showQuote, source } }) => ({
          rating: Number(rating),
          quote: showQuote,
          source,
        })
      ) || []

    const upcomingPerformances = (performancesRes as PerformanceType).map(
      ({ id, startDate }) => ({
        date: startDate,
        id,
      })
    )

    const planYourVisitContent = pageData.productionPlanYourVisit?.[0]?.content
    const planYourVisit = planYourVisitContent
      ? {
          imageData: {
            src: planYourVisitContent.image?._url
              ? getCloudinaryUrl(
                  planYourVisitContent.image?._url,
                  {
                    width: 600,
                    aspectRatio: '16:9',
                    type: 'pad',
                  },
                  { background: 'auto' }
                )
              : '',
            alt: planYourVisitContent.image?.altTag,
          },
          text: {
            description: planYourVisitContent.description,
          },
          linksVariant: planYourVisitContent.linksVariant,
          links: planYourVisitContent.links?.map((link) => ({
            linkText: link.name,
            link: link.url,
            openInNewTab: Boolean(link.target),
          })),
        }
      : undefined

    const tickets = showSideBar
      ? {
          standardPricing: pageData.productionStandardPricing,
          subscriptionInfo: pageData.productionSubscribersInfo,
          accessInfo: pageData.productionAccessInfo,
          resaleInfo: pageData.productionResaleInfo,
          groupsInfo: pageData.productionGroupsInfo,
        }
      : undefined

    const status = result?.status || 'closed'

    const buyTicketsCta = {
      label:
        pageData.ctaOverride ||
        t('aggregatorSiteShowPage.buyTicketsButton.label'),
      href:
        pageData.externalURL ||
        `/events/${inventorySlug}/${pageData.venueInfo.venueSlug}/calendar/`,
    }

    const getBuyTicketsCta = () => {
      if (status === 'general-onsale') {
        return buyTicketsCta
      }

      if (status === 'sold-out' && pageData.resellAvailable) {
        return {
          label: t('aggregatorSiteShowPage.buyTicketsButton.label'),
          href: pageData.resellURL.url,
        }
      }
      return undefined
    }

    const generalOnSale = result?.statusDates?.generalOnSale

    const hero = {
      videoUrl: pageData.videoEmbed,
      location: result?.venue?.location?.city,
      description: pageData.shortShowDescription,
      heroImage: pageData.aggregatorHeroImage?._url
        ? getAggregatorSiteHeroCloudinaryImages({
            imageUrl: pageData.aggregatorHeroImage._url,
          })
        : '',
      imageGallery: pageData.productionGallery.map((image) => ({
        alt: image.media.altTag,
        src: getCloudinaryImageUrlAutoQualityAndFormat(image.src),
        srcSets: getCloudinaryMediaGallerySrcSet(image.src, 'Grid', true),
      })),
      lifecycle: getShowPageHeroLifecycleByStatus({
        status,
        preSales: result?.statusDates?.preSales,
      }),
      lifecycleSubtitle:
        status === 'sold-out' && pageData.resellAvailable
          ? t('aggregatorSiteShowPage.hero.resaleText')
          : undefined,
      cta: getBuyTicketsCta(),
      infoBarContent: getHeroInfoBarByStatus({
        status,
        generalOnSale,
      }),
    }

    return (
      <AggregatorSiteShowPage
        eventName={pageData.eventName}
        resolvedInfoBar={resolvedInfoBar}
        venueInfo={pageData.venueInfo}
        websiteSlug={websiteSlug}
        inventorySlug={inventorySlug}
        breadcrumbs={breadcrumbs}
        faqs={pageData.productionFaqs?.map((faq) => ({ ...faq.content })) || []}
        castList={
          pageData.productionCastCrew?.map((item) => ({
            ...item.content,
          })) || []
        }
        upsell={upsell}
        soundtrack={pageData?.productionSoundtrack}
        goodToKnowInfoItems={goodToKnow}
        goodToKnowLink={goodToKnowLink}
        reviews={reviews}
        upcomingPerformances={upcomingPerformances}
        schedule={pageData.performanceTimes || pageData.startTime || undefined}
        venueName={result?.venueName || pageData.venueInfo.venueConfigName}
        venueDistrict={result?.venueTheatreDistrict}
        venueAddress={result?.venue?.location || undefined}
        runningTime={result?.runningTime || undefined}
        genres={result?.genres || pageData.genreList || []}
        description={pageData?.productionShowPageDescription}
        accessPerformances={
          result?.accessDates?.map(({ accessType, date, sourceId }) => ({
            type: accessType,
            date: date,
            sourceId: sourceId,
          })) || []
        }
        planYourVisit={planYourVisit}
        venueAccessibility={{
          features: pageData?.productionVenueAccessibilityFeatures || [],
          image: pageData?.productionVenueAccessibilityImage || null,
        }}
        preSales={result?.statusDates?.preSales || undefined}
        tickets={tickets}
        hero={hero}
        buyTicketsCta={getBuyTicketsCta()}
        showSidebarCta={showSideBar}
      />
    )
  } catch (error) {
    if (error instanceof NotFoundError) {
      return notFound()
    } else {
      console.error(`Error: ${error}`)
      throw error
    }
  }
}

export async function generateMetadata({
  params,
}: {
  params: Props
}): Promise<Metadata> {
  try {
    const { slug } = await params
    const pageUrl = getPathFromSlug(`/events/${slug}`)
    const pageData = await getPageContent(pageUrl)
    const seoFields = getSeoFields(pageData)

    const productionImage =
      pageData.openGraphImage ||
      pageData.productionShotTall ||
      pageData.productionShot

    return {
      ...seoFields,

      description: pageData.seoDescription || pageData.eventSubName,

      openGraph: {
        ...seoFields.openGraph,
        images: productionImage?._url
          ? [
              {
                url: getShareImage(productionImage?._url) || '',
                alt: productionImage?.altTag,
              },
            ]
          : undefined,
      },
    }
  } catch (e) {
    // failing gracefully - no need to throw an error
    return {}
  }
}

export default ShowPageRoute

export const dynamic = 'force-static'
