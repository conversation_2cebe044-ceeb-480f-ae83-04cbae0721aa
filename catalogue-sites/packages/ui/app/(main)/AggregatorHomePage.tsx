'use client'

import { ParsedUrlQuery } from 'querystring'

import {
  InfoBar,
  type BreadcrumbsProps,
  Box,
  Link,
  Grid,
  Stack,
  ContentCard,
  InfoSection,
  type InfoItemProps,
  RichTextParserInline,
  type IconPath,
  InPageNavigation,
  SectionTitle,
  ContentCards,
} from '@atg-digital/ui-components'
import React, { ReactNode } from 'react'
import { useTheme, SxProps } from '@mui/material'
import { t } from 'i18next'

import { BreadcrumbsWithSchema } from 'components/BreadcrumbsWithSchema'
import { ShowcaseCarousel } from 'components/ShowcaseCarousel'
import { TrustpilotWidget } from 'components/TrustpilotWidget'
import { ContentCardListWithTitle } from 'components/ContentCardListWithTitle'
import { HomepageSearchWrapper } from 'components/SearchBar'
import { getCloudinaryUrl } from 'utils/cloudinary-url'
import { enrichGenreWithLabel } from 'data/lists/genres-list'
import { emitGeneralEvent } from 'lib/dataLayer/general'
import { brandAlias } from 'utils/site/brand-alias'
import type { CuratedContent } from 'data/typesense/get-curated-content'
import { ShowListWithTitle } from 'components/ShowsListWithTitle'
import { ShowcaseItem } from 'data/typesense/homepage/map-showcase-item'

type PopularSearch = {
  name: string
  url: string
  target?: string
}

type Link = {
  name: string
  url: string
  target?: '_blank' | 'self'
  onClick?: () => void
}

type ContentCard = {
  image: { _url: string; altTag?: string } | null
  cardLink: Link | null
  subtitle: string
  title?: string
  description?: string
  findOutMoreLink?: Link | null
}

type InfoSectionItem = {
  infoItemIcon: IconPath
  infoItemTitle: string
  infoItemText: string
  infoItemLink?: Link | null
}

export interface BlockPageProps {
  contentTypeAlias?: string
  pageUrl?: string
  resolvedInfoBar?: string[]
  breadcrumbs?: BreadcrumbsProps['breadcrumbs']
  popularSearches: PopularSearch[]
  showcaseItems: ShowcaseItem[]
  curatedContent?: CuratedContent[]
  yourVisitItems: ContentCard[]
  spotlight: ContentCard[]
  aboutAtgItems: InfoSectionItem[]
  genreList?: string[]
}

export interface BlockPageParams extends ParsedUrlQuery {
  slug?: string
  page?: string[]
}

function GridContainer({
  children,
  customSx = {},
}: {
  children: React.ReactElement | React.ReactElement[]
  customSx?: SxProps
}) {
  const { tokens } = useTheme()

  return (
    <Grid
      container
      columns={12}
      display="flex"
      justifyContent="center"
      maxWidth={tokens.page_innerContainer_maxWidth}
      margin="auto"
      sx={customSx}
    >
      {children}
    </Grid>
  )
}

function GenresList({ items }: { items: string[] }) {
  const { tokens } = useTheme()

  if (!items?.length) return null

  const inPageButtons = items
    ?.map((genre) => {
      const matched = enrichGenreWithLabel(genre)

      if (!matched) {
        return null
      }

      return {
        label: matched?.label,
        href: `/whats-on/${brandAlias === 'atgus' ? 'us' : 'uk'}/${genre}/`,
        icon: matched?.icon || 'ui_allGenres',
        onClick: () => emitGeneralEvent('onClickGenreHome', matched?.label),
      }
    })
    .filter((genre) => genre !== null)

  return (
    <GridContainer
      customSx={{
        width: '100%',
      }}
    >
      <Grid size={{ xs: 11, lg: 10 }}>
        <Stack
          gap={tokens.foundations_styles_spacingScaleNumeric_5}
          overflow="hidden"
        >
          <SectionTitle title={t('homepage.genreBar.title')} />
          <InPageNavigation
            inPageButton={inPageButtons}
            customGap={{
              xs: tokens.system_spacing_xxs,
              lg: tokens.system_spacing_xs,
            }}
            customSelectorMinWidth={{ xs: 'auto', lg: '80px' }}
          />
        </Stack>
      </Grid>
    </GridContainer>
  )
}

function YourVisit({ items }: { items: ContentCard[] }) {
  if (!items?.length) return null

  return (
    <Box sx={{ backgroundColor: '#F4ECFF' }}>
      <GridContainer>
        <Grid size={{ xs: 11, lg: 10 }}>
          <ContentCardListWithTitle contentCards={items} />
        </Grid>
      </GridContainer>
    </Box>
  )
}

function Spotlight({ items }: { items: ContentCard[] }) {
  const { tokens } = useTheme()

  if (!items?.length) return null

  return (
    <Box
      sx={{
        backgroundColor: tokens.foundations_palette_grey_100,
      }}
    >
      <GridContainer>
        <Grid size={{ xs: 11, lg: 10 }}>
          <Stack
            gap={tokens.foundations_styles_spacingScaleNumeric_5}
            py={{
              xs: tokens.foundations_styles_spacingScaleNumeric_8,
              lg: tokens.foundations_styles_spacingScaleNumeric_10,
            }}
          >
            <SectionTitle title={t('homepage.spotlight.title')} />

            <ContentCards
              contentCards={items.map((contentCard) => ({
                imageData: {
                  alt: '',
                  src: contentCard.image?._url
                    ? getCloudinaryUrl(
                        contentCard.image._url,
                        {
                          width: 600,
                          aspectRatio: '16:9',
                          type: 'pad',
                        },
                        { background: 'auto' }
                      )
                    : '',
                },
                text: {
                  title: contentCard.title,
                  subtitle: contentCard.subtitle,
                  description: contentCard.description,
                },
                links: [
                  {
                    link: contentCard?.findOutMoreLink?.url,
                    linkText: contentCard?.findOutMoreLink?.name,
                  },
                ],
                linksVariant: 'text',
              }))}
              imageAspectRatio="16/9"
              maxColumns={2}
              variant="vertical"
            />
          </Stack>
        </Grid>
      </GridContainer>
    </Box>
  )
}

function AboutAtg({ items }: { items: InfoSectionItem[] }) {
  const { tokens } = useTheme()

  if (!items?.length)
    // Adds in some fall back spacing, just in case there is no About block
    return (
      <Box
        paddingBottom={{
          xs: tokens.foundations_styles_spacingScaleNumeric_8,
          lg: tokens.foundations_styles_spacingScaleNumeric_10,
        }}
      />
    )

  return (
    <Box sx={{ backgroundColor: '#F4ECFF' }}>
      <GridContainer
        customSx={{
          py: {
            xs: tokens.foundations_styles_spacingScaleNumeric_8,
            lg: tokens.foundations_styles_spacingScaleNumeric_10,
          },
        }}
      >
        <Grid size={{ xs: 11, lg: 10 }}>
          <Stack>
            <SectionTitle title={t('homepage.aboutAtg.title')} />
            <InfoSection
              isInContentBlock={false}
              variant="large"
              infoItems={items.map((item) => {
                const textContainsHtml =
                  item.infoItemText && /<[a-z][\s\S]*>/i.test(item.infoItemText)

                return {
                  icon: item.infoItemIcon,
                  title: item.infoItemTitle,
                  text: (textContainsHtml
                    ? null
                    : item.infoItemText) as InfoItemProps['text'],
                  children: textContainsHtml ? (
                    <RichTextParserInline
                      richText={item.infoItemText as string}
                    />
                  ) : null,
                  link: item.infoItemLink?.name
                    ? {
                        text: item.infoItemLink?.name,
                        url: item.infoItemLink?.url,
                        target: item.infoItemLink?.target,
                        onClick: () =>
                          emitGeneralEvent(
                            'onClickWhyAtgTicketsLearnMore',
                            item.infoItemLink?.name || ''
                          ),
                      }
                    : undefined,
                }
              })}
            />
          </Stack>
        </Grid>
      </GridContainer>
    </Box>
  )
}

function CuratedContentSection({ section }: { section: CuratedContent }) {
  return (
    <Box>
      <GridContainer>
        <Grid size={{ xs: 11, lg: 10 }}>
          <ShowListWithTitle title={section.heading} items={section.items} />
        </Grid>
      </GridContainer>
    </Box>
  )
}

export const AggregatorHomePage = ({
  resolvedInfoBar,
  breadcrumbs,
  popularSearches,
  showcaseItems,
  curatedContent,
  yourVisitItems,
  spotlight,
  aboutAtgItems,
  genreList = [],
}: BlockPageProps) => {
  const { tokens } = useTheme()

  const interleavedContent: ReactNode[] = []

  const firstTwoCurated = curatedContent?.slice(0, 2) || []
  if (firstTwoCurated.length > 0) {
    interleavedContent.push(
      <Stack
        key="curated-block-1"
        gap={tokens.foundations_styles_spacingScaleNumeric_7}
      >
        {firstTwoCurated.map((item) => (
          <CuratedContentSection key={item.id} section={item} />
        ))}
      </Stack>
    )
  }

  interleavedContent.push(<YourVisit key="your-visit" items={yourVisitItems} />)

  const nextTwoCurated = curatedContent?.slice(2, 4) || []
  if (nextTwoCurated.length > 0) {
    interleavedContent.push(
      <Stack
        key="curated-block-2"
        gap={tokens.foundations_styles_spacingScaleNumeric_7}
      >
        {nextTwoCurated.map((item) => (
          <CuratedContentSection key={item.id} section={item} />
        ))}
      </Stack>
    )
  }

  interleavedContent.push(<Spotlight key="spotlight" items={spotlight} />)

  const remainingCurated = curatedContent?.slice(4) || []
  if (remainingCurated.length > 0) {
    interleavedContent.push(
      <Stack
        key="curated-block-3"
        gap={tokens.foundations_styles_spacingScaleNumeric_7}
      >
        {remainingCurated.map((item) => (
          <CuratedContentSection key={item.id} section={item} />
        ))}
      </Stack>
    )
  }

  return (
    <>
      {breadcrumbs && <BreadcrumbsWithSchema breadcrumbs={breadcrumbs} />}

      {resolvedInfoBar && <InfoBar>{resolvedInfoBar}</InfoBar>}

      {/* {isHomePage && venueInfo && <BusinessSchema data={venueInfo} />} */}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: { xs: tokens.system_spacing_xl, lg: tokens.system_spacing_xxl },
        }}
      >
        <Box>
          <HomepageSearchWrapper
            popularSearches={popularSearches?.map(({ name, url }) => ({
              label: name,
              id: name,
              url,
            }))}
          />

          {showcaseItems?.length > 0 && (
            <ShowcaseCarousel showcaseItems={showcaseItems} />
          )}
        </Box>

        {brandAlias === 'atgtk' && (
          <GridContainer>
            <Grid size={{ xs: 10, lg: 7 }}>
              <TrustpilotWidget />
            </Grid>
          </GridContainer>
        )}

        <GenresList items={genreList} />

        {interleavedContent.map((node) => node)}

        <AboutAtg items={aboutAtgItems} />
      </Box>
    </>
  )
}
