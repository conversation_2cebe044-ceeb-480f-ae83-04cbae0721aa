import { useTheme } from '@mui/material'
import { Box, SectionTitle, ShowCard, Grid } from '@atg-digital/ui-components'

import { Carousel } from 'components/Carousel'
import { emitGeneralEvent } from 'lib/dataLayer/general'
import { Event } from 'data/typesense/get-curated-content'

type Link = {
  name: string
  url: string
}

interface Props {
  title?: string | null
  items?: Event[] | null
  seeAllLink?: Link | null
}

export const ShowListWithTitle = ({ title, items }: Props) => {
  const { tokens } = useTheme()

  return (
    <Box
      gap={tokens.components_sectionTitle_marginBottom}
      display="flex"
      flexDirection="column"
    >
      {title && (
        <Box>
          <SectionTitle
            title={title}
            // TODO: We need this from the curated content
            // link={
            //   showsList.seeAllLink
            //     ? {
            //         href: showsList.seeAllLink.url,
            //         label: t('homepage.seeAll'),
            //         onClick: () =>
            //           emitGeneralEvent(
            //             'onClickSeeOtherShows',
            //             showsList.seeAllLink?.url ?? ''
            //           ),
            //       }
            //     : undefined
            // }
          />
        </Box>
      )}
      {items && items?.length > 0 && (
        <>
          <Box display={{ xs: 'block', lg: 'none' }}>
            <Carousel
              contents={items?.map((show) => (
                <ShowCard
                  height="100%"
                  key={show.id}
                  dates="dates"
                  {...show}
                  trackImageClickEvent={() =>
                    emitGeneralEvent(
                      'onClickCarouselImage',
                      show.slug || show.title
                    )
                  }
                />
              ))}
              contentGap={tokens.foundations_styles_spacingScaleNumeric_5}
            />
          </Box>
          <Box display={{ xs: 'none', lg: 'flex' }} width="100%">
            <Grid
              container
              columns={12}
              columnSpacing={tokens.foundations_styles_spacingScaleNumeric_5}
              rowSpacing={0}
              width="100%"
              flexGrow={1}
            >
              {items.slice(0, 4).map((show) => (
                <Grid key={show.id} size={{ lg: 3 }}>
                  <ShowCard
                    height="100%"
                    key={show.id}
                    dates="test"
                    {...show}
                    trackImageClickEvent={() =>
                      emitGeneralEvent(
                        'onClickCarouselImage',
                        show.slug || show.title
                      )
                    }
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        </>
      )}
    </Box>
  )
}
