'use client'

import { Suspense, useEffect, useRef, useState, useTransition } from 'react'
import { useSearchParams } from 'next/navigation'
import { useDebouncedCallback } from 'use-debounce'
import {
  But<PERSON>,
  Divider,
  removeCommas,
  RichTextParserInline,
  TextField,
  Link,
  CircularLoader,
  IconButton,
} from '@atg-digital/ui-components'
import {
  SearchIcon,
  LocationOnIcon,
  TicketStarIcon,
  VenueFacadeIcon,
  CloseIcon,
  type BackdropIconComponent,
} from '@atg-digital/ui-icons'
import {
  Popper,
  Paper,
  useTheme,
  Box,
  Typography,
  ListItem,
  useMediaQuery,
} from '@mui/material'

import type {
  SearchSection,
  SectionType,
} from '../../data/typesense/get-search-autocomplete'

import { emitGeneralEvent } from 'lib/dataLayer/general'
import { emitSearchEvent } from 'lib/dataLayer/search'

const notEnoughChars = 'Type at least 3 characters for results'
const noResults = 'No results found'
const fullSearchBarWidth = '320px'
const LISTBOX_ID = 'search-results-listbox'

const sectionMap: Record<
  SectionType,
  { title: string; icon: BackdropIconComponent }
> = {
  show: {
    title: 'Shows',
    icon: TicketStarIcon,
  },
  location: {
    title: 'Locations',
    icon: LocationOnIcon,
  },
  venue: {
    title: 'Venues',
    icon: VenueFacadeIcon,
  },
}

type Props = {
  isWebView?: boolean
  isHomepage?: boolean
}

function SearchMessage({
  children,
  withLoader = false,
}: {
  children: React.ReactNode
  withLoader?: boolean
}) {
  const { tokens } = useTheme()
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        padding: removeCommas(tokens.components_searchBar_paddingItem),
      }}
    >
      {withLoader && (
        <CircularLoader
          sx={{ color: tokens.components_searchBar_fg }}
          size={20}
        />
      )}
      <Typography
        sx={{
          marginLeft: withLoader
            ? tokens.components_searchBar_spacer
            : undefined,
        }}
        variant="bodySmall"
        color={tokens.components_searchBar_fg}
      >
        {children}
      </Typography>
    </Box>
  )
}

function SearchBar({ isWebView, isHomepage }: Props) {
  const containerRef = useRef<HTMLDivElement>(null)
  const popperContentRef = useRef<HTMLDivElement>(null)
  const searchParams = useSearchParams()
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isPending, startTransition] = useTransition()
  const [message, setMessage] = useState<string>(notEnoughChars)
  const [results, setResults] = useState<SearchSection[]>([])

  // Index of the "active/hovered" option (for aria-activedescendant)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const linkRefs = useRef<(HTMLAnchorElement | null)[]>([])

  const getTotalItems = () => {
    if (results.length === 0) return 0
    let total = 0
    for (const section of results) {
      if (isWebView && section.type === 'venue') continue
      total += section.items.length
    }
    return total + 1 // View all results
  }

  useEffect(() => {
    const term = searchParams.get('q')?.toString()
    if (term) {
      setSearchTerm(term)
      handleSearch(term, false) // run once in the background so results appear on click
    }
  }, [searchParams])

  useEffect(() => {
    setSelectedIndex(-1)
    linkRefs.current = []
  }, [results])

  const { tokens, breakpoints } = useTheme()

  const isSmDown = useMediaQuery(breakpoints.down('sm'))

  // Handle body overflow for isWebView on mobile only
  useEffect(() => {
    if (!isWebView) return

    // Check if mobile (you could also use a more robust method or media query hook)

    if (isOpen && isSmDown) {
      document.documentElement.style.overflow = 'hidden'
      document.body.style.overflow = 'hidden'
      document.documentElement.style.overscrollBehavior = 'none'
      document.body.style.overscrollBehavior = 'none'
    } else {
      document.body.style.overflow = ''
      document.documentElement.style.overflow = ''
      document.documentElement.style.overscrollBehavior = ''
      document.body.style.overscrollBehavior = ''
    }

    // Cleanup on unmount or when effect re-runs
    return () => {
      document.body.style.overflow = ''
      document.documentElement.style.overflow = 'hidden'
    }
  }, [isOpen, isWebView, isSmDown])

  // Close on click outside (or Escape), scoped to this instance.
  useEffect(() => {
    if (!isOpen) return
    const onPointerDown = (e: PointerEvent | MouseEvent | TouchEvent) => {
      const target = e.target as Node | null
      if (!target) return
      const clickedInsideInput =
        containerRef.current && containerRef.current.contains(target)
      const clickedInsidePopper =
        popperContentRef.current && popperContentRef.current.contains(target)
      if (!clickedInsideInput && !clickedInsidePopper) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }
    document.addEventListener('pointerdown', onPointerDown, true)
    return () => {
      document.removeEventListener('pointerdown', onPointerDown, true)
    }
  }, [isOpen])

  const handleSearch = useDebouncedCallback((term: string, canOpen = true) => {
    if (term.length === 0) {
      setIsOpen(false)
      setMessage(notEnoughChars)
      setSearchTerm(term)
      setResults([])
      return
    }
    if (term.length < 3) {
      setIsOpen(canOpen)
      setMessage(notEnoughChars)
      setSearchTerm(term)
      setResults([])
      return
    } else {
      startTransition(async () => {
        setIsOpen(canOpen)
        setMessage(noResults)
        setSearchTerm(term)

        try {
          const response = await fetch(`/api/autocomplete/?q=${term}`)
          const results = await response.json()

          setResults(results)
        } catch (error) {
          console.error('Error fetching search results:', error)
          setResults([])
          return
        }
      })
    }
  }, 200)

  const anchorEl = useRef<HTMLDivElement>(null)

  const handleClear = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    e.currentTarget.blur()
    setSearchTerm('')
    if (anchorEl.current) {
      const inputElement = anchorEl.current.querySelector('input')
      if (inputElement) {
        ;(inputElement as HTMLInputElement).value = ''
      }
      setIsOpen(false)
      handleSearch('')
      setMessage(notEnoughChars)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSearch(e.target.value)
  }

  // Keyboard handling is **scoped to the input** (no global listeners).
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isOpen) return
    const total = getTotalItems()
    if (total === 0) return

    switch (e.key) {
      case 'ArrowDown': {
        e.preventDefault()
        setSelectedIndex((prev) => {
          const next = prev + 1
          return next >= total ? 0 : next
        })
        break
      }
      case 'ArrowUp': {
        e.preventDefault()
        setSelectedIndex((prev) => {
          const next = prev - 1
          return next < 0 ? total - 1 : next
        })
        break
      }
      case 'Enter': {
        // If an option is active, activate it; otherwise let the form submit naturally.
        if (selectedIndex >= 0 && linkRefs.current[selectedIndex]) {
          e.preventDefault()
          linkRefs.current[selectedIndex]?.click()
        }
        break
      }
      case 'Escape': {
        e.preventDefault()
        setIsOpen(false)
        setSelectedIndex(-1)
        break
      }
      default:
        break
    }
  }

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // If focus stays within the popper (e.g., clicking a result), keep selection;
    // otherwise, clear it so the visual highlight doesn't linger on Tab.
    const next = e.relatedTarget as HTMLElement | null
    if (!next || !popperContentRef.current?.contains(next)) {
      setSelectedIndex(-1)
    }
  }

  const headerWidth = isHomepage
    ? fullSearchBarWidth
    : tokens.components_searchBar_width_input

  const searchHeight = {
    minHeight: tokens.components_searchBar_height_full,
    height: tokens.components_searchBar_height_full,
  }

  const containerStyle = {
    width: { xs: '100%', sm: headerWidth },
    height: tokens.components_searchBar_height_full,
    py: removeCommas(tokens.components_searchBar_padding),
    boxSizing: 'border-box',
    position: 'relative',
  }

  const textFieldStyle = {
    ...searchHeight,
    'boxSizing': 'border-box',
    'borderRadius': tokens.components_searchBar_borderRadius,
    'justifyContent': 'center',
    'color': tokens.components_searchBar_fg,
    'margin': 0,
    'padding': 0,
    'width': '100%',
    // TODO: Something to fix properly
    '.MuiInputBase-root': {
      marginTop: 0,
      ...searchHeight,
    },
    '.MuiInputBase-input': {
      ...searchHeight,
      paddingRight: '78px',
    },
    'backgroundColor': 'white',
  }

  const clearButtonStyle = {
    'position': 'absolute',
    'right': '50px',
    'padding': '8px',
    'top': '50%',
    'cursor': 'pointer',
    'transform': 'translateY(-50%)',
    'background': 'transparent',
    'pointerEvents': isOpen ? 'auto' : 'none',
    'borderRadius': '0',
    '&:focus, &:hover': {
      outline: 'none',
      border: 'none',
      background: 'transparent',
    },
    '& svg': {
      outline: 'none',
      border: 'none',
      background: 'transparent',
      transition: 'opacity 0.3s ease',
      opacity: isOpen ? '1' : '0',
    },
  }

  const buttonStyle = {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    minWidth: '50px',
    alignContent: 'center',
    padding: removeCommas(tokens.components_searchBar_button_padding),
    backgroundColor: tokens.components_searchBar_button_bg,
    color: tokens.components_searchBar_button_fg,
    borderRadius: removeCommas(tokens.components_searchBar_button_borderRadius),
  }

  const popperStyle = {
    'zIndex': isHomepage ? 5 : 12,
    'width': { xs: '100%', sm: headerWidth },
    'maxWidth': 'calc(100% - 26px)',
    '& .MuiPaper-root > .MuiBox-root': { padding: 0 },
    // Override Popper positioning for mobile webView
    ...(isWebView && {
      [breakpoints.down('sm')]: {
        position: 'fixed',
        top: 'auto',
        left: '0',
        right: '0',
        bottom: '0',
        transform: 'none !important',
        width: '100vw',
        maxWidth: '100vw',
      },
    }),
  }

  const paperStyle = {
    p: removeCommas(tokens.components_searchBar_padding),
    background: '#fff',
    // Mobile webView styles
    ...(isWebView && {
      [breakpoints.down('sm')]: {
        px: 0,
        borderRadius: 0,
        py: tokens.system_spacing_m,
        boxShadow: 'none',
        position: 'fixed',
        left: 0,
        right: 0,
        bottom: 0,
        height: 'calc(100% - 144px);',
        width: '100%',
        overflowY: 'auto',
      },
    }),
  }

  const linkStyle = {
    'width': '100%',
    'display': 'block',
    'padding': removeCommas(tokens.components_searchBar_paddingItem),
    '&:hover, &:focus': {
      color: tokens.components_searchBar_fg,
      backgroundColor: tokens.components_searchBar_hover,
    },
    'mark': { fontWeight: 'bold', background: 'transparent' },
  }

  const listLinkStyle = { ...linkStyle, textDecoration: 'none' }
  const viewAllStyle = {
    ...linkStyle,
    fontWeight: tokens.foundations_typography_fontWeight_semibold,
  }

  const listStyle = {
    'padding': removeCommas(tokens.components_searchBar_paddingItem),
    'backgroundColor': 'inherit',
    'display': 'flex',
    'alignItems': 'center',
    '& > svg': {
      color: tokens.components_searchBar_title,
      marginRight: tokens.foundations_styles_spacingScaleNumeric_2,
    },
  }

  const getItemStyle = (index: number, baseStyle) => ({
    ...baseStyle,
    backgroundColor:
      selectedIndex === index
        ? tokens.components_searchBar_hover
        : 'transparent',
    outlineOffset: '-2px',
  })

  let itemIndex = 0

  return (
    <>
      <Box
        sx={containerStyle}
        ref={containerRef}
        onClick={() => {
          isHomepage && emitGeneralEvent('onClickSearchBarHome', '')
        }}
      >
        <form
          action="/whats-on/"
          method="get"
          onSubmit={() => {
            emitSearchEvent(searchTerm)
          }}
        >
          <TextField
            ref={anchorEl}
            aria-describedby="simple-popper"
            type="text"
            autoComplete="off"
            onChange={handleChange}
            name="q"
            defaultValue={searchTerm}
            sx={textFieldStyle}
            placeholder="Search"
            onFocus={() => {
              if (searchTerm.length > 1) setIsOpen(true)
              emitGeneralEvent('onClickSearchBarFocus', '')
            }}
            slotProps={{
              htmlInput: {
                'role': 'combobox',
                'onKeyDown': handleInputKeyDown,
                'onBlur': handleInputBlur,
                'aria-expanded': isOpen,
                'aria-controls': LISTBOX_ID,
                'aria-autocomplete': 'list',
                'aria-activedescendant':
                  selectedIndex >= 0
                    ? `search-option-${selectedIndex}`
                    : undefined,
              },
            }}
          />
          <IconButton
            onClick={handleClear}
            sx={clearButtonStyle}
            aria-label="Clear search"
            ariaLabel="Clear search"
            size="medium"
            tabIndex={isOpen ? 0 : -1}
            icon={<CloseIcon color={tokens.components_searchBar_fg} />}
          />
          <Button
            sx={buttonStyle}
            type="submit"
            fullWidth={false}
            onClick={() => {
              emitGeneralEvent('onClickSearchIcon', '')
            }}
          >
            <SearchIcon />
          </Button>
        </form>
      </Box>

      <Popper
        id="search-results"
        open={isOpen}
        anchorEl={anchorEl.current}
        placement="bottom-start"
        sx={popperStyle}
        ref={popperContentRef}
      >
        <Paper sx={paperStyle}>
          <Box
            padding={isWebView ? '28px 24px' : '28px 24px'}
            borderRadius={isWebView ? '0' : '8px'}
          >
            {isPending ? (
              <SearchMessage withLoader>Searching...</SearchMessage>
            ) : results.length === 0 ? (
              <SearchMessage>{message}</SearchMessage>
            ) : (
              <>
                {results.map((section) => {
                  if (isWebView && section.type === 'venue') return null
                  const meta = sectionMap[section.type] || {}
                  return (
                    <Box key={meta.title} className="group-container">
                      <Box sx={listStyle}>
                        {meta.icon && <meta.icon size="22px" />}
                        <Typography
                          variant="labelLarge"
                          color={tokens.components_searchBar_title}
                        >
                          {meta.title}
                        </Typography>
                      </Box>
                      <Box
                        component="ul"
                        id={LISTBOX_ID}
                        sx={{ listStyle: 'none', padding: 0, margin: 0 }}
                        role="listbox"
                      >
                        {section.items.map((item) => {
                          const currentIndex = itemIndex
                          itemIndex++

                          return (
                            <ListItem
                              key={item.id}
                              sx={{ padding: 0 }}
                              role="option"
                              aria-selected={selectedIndex === currentIndex}
                              id={`search-option-${currentIndex}`}
                            >
                              <Link
                                ref={(el) => {
                                  linkRefs.current[currentIndex] = el
                                }}
                                color={tokens.components_searchBar_fg}
                                variant="bodySmall"
                                href={item.href}
                                sx={getItemStyle(currentIndex, listLinkStyle)}
                                tabIndex={-1} // keep options out of tab order
                                onClick={() => {
                                  setIsOpen(false)
                                  emitSearchEvent(searchTerm)
                                }}
                                onMouseEnter={() =>
                                  setSelectedIndex(currentIndex)
                                }
                                onMouseLeave={() => setSelectedIndex(-1)}
                              >
                                <Typography variant="bodySmall" component="p">
                                  <RichTextParserInline richText={item.label} />
                                </Typography>

                                {item.subLabel && (
                                  <Typography
                                    variant="caption"
                                    component="p"
                                    sx={{
                                      color:
                                        tokens.system_foreground_fill_fourthElevation,
                                    }}
                                  >
                                    <RichTextParserInline
                                      richText={item.subLabel}
                                    />
                                  </Typography>
                                )}
                              </Link>
                            </ListItem>
                          )
                        })}
                      </Box>
                      <Divider
                        className="group-divider"
                        sx={{
                          marginY: removeCommas(
                            tokens.components_searchBar_spacer
                          ),
                        }}
                      />
                    </Box>
                  )
                })}
                <ListItem
                  sx={{ padding: 0 }}
                  role="option"
                  id={`search-option-${itemIndex}`}
                >
                  <Link
                    ref={(el) => {
                      linkRefs.current[itemIndex] = el
                    }}
                    color={tokens.components_searchBar_fg}
                    variant="bodySmall"
                    href={`/whats-on/?q=${searchTerm}`}
                    sx={getItemStyle(itemIndex, viewAllStyle)}
                    onClick={() => {
                      setIsOpen(false)
                      emitGeneralEvent(
                        'onClickSearchViewAllResults',
                        searchTerm
                      )
                    }}
                    tabIndex={-1}
                  >
                    View all results
                  </Link>
                </ListItem>
              </>
            )}
          </Box>
        </Paper>
      </Popper>
    </>
  )
}

export const TypesenseSearch = ({
  isWebView = false,
  isHomepage = false,
}: Props) => (
  <Suspense>
    <SearchBar isWebView={isWebView} isHomepage={isHomepage} />
  </Suspense>
)
