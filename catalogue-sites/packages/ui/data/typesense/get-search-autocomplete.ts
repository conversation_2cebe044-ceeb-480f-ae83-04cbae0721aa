'use server'

import { MultiSearchRequestsSchema } from 'typesense/lib/Typesense/MultiSearch'
import { unstable_cache } from 'next/cache'

import { showPrefix } from '../../constants'

import { typesense } from './typesense-client'
import { DocumentSchema } from './listing-page/types'
import { buildFilterBy } from './listing-page/build-filter-by'

import { brandAlias } from 'utils/site/brand-alias'
import { locationLabelToSlug } from 'data/lists/locations-list'

export type SectionType = 'show' | 'location' | 'venue'

type SearchBarResults = {
  id: string
  label: string
  subLabel?: string
  href: string
}

export type SearchSection = {
  type: SectionType
  items: SearchBarResults[]
}

export const getSearchbarAutocomplete = unstable_cache(
  async function (searchTerm: string): Promise<SearchSection[]> {
    try {
      const currentCountry =
        brandAlias === 'atgus' ? 'United States' : 'United Kingdom'
      const searchRequests: MultiSearchRequestsSchema<DocumentSchema, string> =
        {
          searches: [
            // showQuery (inc tours)
            {
              collection: 'show',
              q: searchTerm,

              query_by: 'title,tourNames,genres,venueName',
              query_by_weights: '6,6,1,1',

              group_by: 'tourIds',
              group_limit: 1, // this will mean not all shows in a tour are returned, but we actually only need one to look up the tour info
              group_missing_values: false,

              include_fields:
                'id,title,slug,venueSlug,venueName,$tour(id,title,slug)',

              filter_by: buildFilterBy({}), // Only really need the default filters here

              per_page: 5,
              highlight_fields: 'title,venueName',

              stopwords: 'common-words',
              sort_by: '_text_match:asc',
            },
            // locationsQuery - using shows for this result would give us slightly better results, but it likely be a more expensive query
            {
              collection: 'venue',
              q: searchTerm,

              query_by: 'theatreDistrict,location.city',
              query_by_weights: '4,1',

              filter_by: `location.country:=\`${currentCountry}\` && isAtgVenue:=true`,

              group_by: 'theatreDistrict',
              group_limit: 1,
              group_missing_values: false,

              include_fields: 'id,theatreDistrict',

              per_page: 2,
              highlight_fields: 'theatreDistrict',

              stopwords: 'common-words',
              sort_by: '_text_match:asc',
            },
            // venuesQuery
            {
              collection: 'venue',
              q: searchTerm,

              query_by: 'name,theatreDistrict,location.city',
              query_by_weights: '4,1,1',

              filter_by: `location.country:=\`${currentCountry}\` && isAtgVenue:=true`,

              include_fields: 'id,name,slug',

              per_page: 2,
              highlight_fields: 'name',

              stopwords: 'common-words',
              sort_by: '_text_match:asc,name:asc',
            },
          ],
        }

      // Used for debugging the query send to Typesense
      // console.log('searchParams', searchRequests)

      const response =
        await typesense.multiSearch.perform<[DocumentSchema, DocumentSchema]>(
          searchRequests
        )

      const [showQuery, locationsQuery, venuesQuery] = response.results

      const resultSections: SearchSection[] = []

      if (showQuery?.grouped_hits && showQuery?.grouped_hits.length > 0) {
        resultSections.push({
          type: 'show',
          items: showQuery.grouped_hits
            .map((hit) => {
              const foundInGroup = hit.found || 0
              const isTour = foundInGroup > 1

              if (isTour) {
                const result = hit?.hits?.[0]
                const document = result.document // this is actually a show document
                const tour = document.tour?.[0]

                if (!tour) {
                  console.warn(
                    'No tour found for hit:',
                    document.id,
                    document.slug
                  )
                  return null
                }

                const tourUrl = `/${showPrefix}/${tour.slug}/`

                return {
                  id: tour.id,
                  label: `${tour.title} Tour`,
                  href: tourUrl,
                }
              }

              const result = hit?.hits?.[0]
              const show = result.document

              const showUrl = `/${showPrefix}/${show.slug}/${show.venueSlug}/`

              return {
                id: show.id,
                label: result.highlight?.title?.snippet || show.title,
                subLabel:
                  result.highlight?.venueName?.snippet || show.venueName,
                href: showUrl,
              }
            })
            .filter((card) => card !== null),
        })
      }

      if (
        locationsQuery?.grouped_hits &&
        locationsQuery?.grouped_hits.length > 0
      ) {
        resultSections.push({
          type: 'location',
          items: locationsQuery.grouped_hits.map((hit) => {
            const result = hit?.hits?.[0]
            const location = result.document

            return {
              id: location.id,
              label:
                result.highlight?.theatreDistrict?.snippet ||
                location.theatreDistrict,
              href: `/whats-on/${locationLabelToSlug(
                location.theatreDistrict
              )}/`,
            }
          }),
        })
      }

      if (venuesQuery?.hits && venuesQuery?.hits.length > 0) {
        resultSections.push({
          type: 'venue',
          items: venuesQuery.hits.map((result) => {
            const venue = result?.document

            return {
              id: venue.id,
              label: result.highlight?.name?.snippet || venue.name,
              href: `/venues/${venue.slug}/`,
            }
          }),
        })
      }

      return resultSections
    } catch (error) {
      throw new Error(`Error fetching data from Typesense: ${error}`)
    }
  },
  ['autocomplete'],
  {
    tags: ['listing'],
    revalidate: 60,
  }
)
