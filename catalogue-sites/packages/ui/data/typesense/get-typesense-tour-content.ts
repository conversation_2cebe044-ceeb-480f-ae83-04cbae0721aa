'use server'

import { cache } from 'react'

import { typesense } from './typesense-client'

type Params = {
  websiteSlug: string
  brandAlias: string
}

export type Result = {
  genres?: string[]
  accessDates?: {
    accessType: string
    date: string
    sourceId: string
  }[]
  hasInterval?: boolean
  status?: string
  brandAlias?: string
  isSeason?: boolean
  websiteSlug?: string
  title?: string
  trailerUrl?: string
  // TODO: Improve this type
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  show?: any[]
}

export const getTypesenseTourContent = cache(
  async ({ websiteSlug, brandAlias }: Params) => {
    try {
      const results = await typesense
        .collections('tour')
        .documents()
        .search({
          q: '*',
          filter_by: `slug:=${websiteSlug} && brandAlias:=${brandAlias} && $show(id:*)`,
          // TODO: we shouldn't need all of these fields, so can remove some
          include_fields: '$show(*, strategy: nest_array)',
          exclude_fields: 'performanceIds',
          page: 1,
          per_page: 1,
        })

      return (results.hits?.[0]?.document as Result) || null
    } catch (error) {
      throw new Error(`Error fetching data from Typesense: ${error}`)
    }
  }
)
