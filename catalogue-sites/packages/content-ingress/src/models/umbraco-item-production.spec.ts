import { UmbracoItem } from './umbraco-item'

describe('UmbracoItem - PRODUCTION type for ATG US', () => {
  const originalEnv = process.env

  beforeEach(() => {
    jest.resetModules()
    process.env = { ...originalEnv }
  })

  afterEach(() => {
    process.env = originalEnv
  })

  describe('ATG US Production detection', () => {
    it('should create PRODUCTION type when BRAND_ALIAS is atgus and contentTypeAlias is production', () => {
      process.env.BRAND_ALIAS = 'atgus'
      
      const item = new UmbracoItem({
        _id: 'prod-123',
        contentTypeAlias: 'production',
        name: 'The Book of Mormon',
        websiteSlug: 'the-book-of-mormon',
        venueInfo: {
          venueSlug: 'saenger-theatre',
          venueConfigName: 'Saenger Theatre'
        }
      } as any)

      expect(item.type).toBe('PRODUCTION')
      expect(item.pageUrl).toBe('the-book-of-mormon/saenger-theatre')
    })

    it('should create PRODUCTION type when brand field contains atgus', () => {
      delete process.env.BRAND_ALIAS
      
      const item = new UmbracoItem({
        _id: 'prod-456',
        contentTypeAlias: 'production',
        name: 'Hamilton',
        brand: 'atgus.com',
        websiteSlug: 'hamilton',
        venueInfo: {
          venueSlug: 'broadway-theatre',
          venueConfigName: 'Broadway Theatre'
        }
      } as any)

      expect(item.type).toBe('PRODUCTION')
      expect(item.pageUrl).toBe('hamilton/broadway-theatre')
    })

    it('should create CONTENT type for ATG UK productions', () => {
      process.env.BRAND_ALIAS = 'atgtk'
      
      const item = new UmbracoItem({
        _id: 'prod-789',
        contentTypeAlias: 'production',
        name: 'The Lion King',
        pageUrl: '/events/the-lion-king/',
        websiteSlug: 'the-lion-king'
      } as any)

      expect(item.type).toBe('CONTENT')
      expect(item.pageUrl).toBe('/events/the-lion-king/')
    })

    it('should create CONTENT type for venue brand productions', () => {
      process.env.BRAND_ALIAS = 'hudson'
      
      const item = new UmbracoItem({
        _id: 'prod-111',
        contentTypeAlias: 'production',
        name: 'Death of a Salesman',
        brand: 'thehudsonbroadway.com',
        pageUrl: '/events/death-of-a-salesman-hudson/',
        websiteSlug: 'death-of-a-salesman'
      } as any)

      expect(item.type).toBe('CONTENT')
      expect(item.pageUrl).toBe('/events/death-of-a-salesman-hudson/')
    })

    it('should throw error when PRODUCTION type missing websiteSlug', () => {
      process.env.BRAND_ALIAS = 'atgus'
      
      expect(() => {
        new UmbracoItem({
          _id: 'prod-error-1',
          contentTypeAlias: 'production',
          name: 'Missing Slug Show',
          venueInfo: {
            venueSlug: 'some-venue'
          }
        } as any)
      }).toThrow('Production missing required fields')
    })

    it('should throw error when PRODUCTION type missing venueSlug', () => {
      process.env.BRAND_ALIAS = 'atgus'
      
      expect(() => {
        new UmbracoItem({
          _id: 'prod-error-2',
          contentTypeAlias: 'production',
          name: 'Missing Venue Show',
          websiteSlug: 'missing-venue-show'
        } as any)
      }).toThrow('Production missing required fields')
    })
  })

  describe('Tour type should remain unchanged', () => {
    it('should create TOUR type regardless of brand', () => {
      process.env.BRAND_ALIAS = 'atgus'
      
      const item = new UmbracoItem({
        _id: 'tour-123',
        contentTypeAlias: 'tour',
        name: 'Hamilton Tour',
        websiteSlug: 'hamilton-uk-tour'
      } as any)

      expect(item.type).toBe('TOUR')
      expect(item.pageUrl).toBe('hamilton-uk-tour')
    })
  })

  describe('PK and SK values', () => {
    it('should set correct PK and SK for PRODUCTION type', () => {
      process.env.BRAND_ALIAS = 'atgus'
      
      const item = new UmbracoItem({
        _id: 'prod-pk-test',
        contentTypeAlias: 'production',
        name: 'Wicked',
        websiteSlug: 'wicked',
        venueInfo: {
          venueSlug: 'gershwin-theatre'
        }
      } as any)

      const keys = item.contentKeys

      expect(keys.PK).toBe('PRODUCTION')
      expect(keys.SK).toBe('wicked/gershwin-theatre')
    })
  })
})