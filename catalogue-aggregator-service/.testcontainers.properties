# Testcontainers Configuration for <PERSON><PERSON>
# This file configures Testcontainers to work with <PERSON><PERSON> instead of <PERSON><PERSON>
# Place this file in your home directory (~/.testcontainers.properties) for global config
# or in the project root for project-specific config

# Disable Ryuk container (required for <PERSON><PERSON> in rootless mode)
# Ryu<PERSON> is responsible for cleaning up containers, but doesn't work well with <PERSON>dman
ryuk.disabled=true

# Docker host configuration for Podman
# Uncomment the appropriate line based on your OS and Podman setup:

# macOS with Podman Desktop (default socket location)
# docker.host=unix:///Users/<USER>/.local/share/containers/podman/machine/podman.sock

# Linux with rootless Podman
#docker.host=unix:///run/user/${UID}/podman/podman.sock

# Linux with rootful Podman
#docker.host=unix:///run/podman/podman.sock

# Alternative: Use environment variable DOCKER_HOST instead of this file
# export DOCKER_HOST=unix:///run/user/${UID}/podman/podman.sock

# Enable container reuse for faster test execution (optional)
# This keeps containers running between test runs
testcontainers.reuse.enable=true

# Privileged mode for <PERSON><PERSON><PERSON> if using rootful <PERSON>dman (uncomment if needed)
#ryuk.container.privileged=true

# Docker client strategy (optional, usually auto-detected)
#docker.client.strategy=org.testcontainers.dockerclient.UnixSocketClientProviderStrategy

# Timeout settings (optional, in seconds)
docker.client.timeout=120
testcontainers.start.timeout=180