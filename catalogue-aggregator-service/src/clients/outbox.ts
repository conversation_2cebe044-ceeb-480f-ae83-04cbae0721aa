import {
  type AttributeValue,
  type QueryCommandInput,
  type WriteRequest,
  BatchWriteItemCommand,
  ConditionalCheckFailedException,
  QueryCommand,
  UpdateItemCommand,
} from '@aws-sdk/client-dynamodb'
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb'

import { chunk, sleep } from '../utils/common'
import { ddbCall } from '../utils/ddb-metric-wrapper'

import { ddbClient } from './ddb'

import type { EventSource } from '../services/data-dispatch.service'

export interface OutboxDbRecord {
  pk: string
  sk: string
  createdAt: string
  attempts?: number
  payload: Buffer | OutboxRecordPayload
  GSI1PK?: string
  emittedAt?: string
}

export interface OutboxRecordPayload {
  EventBusName: string
  DetailType: 'publish'
  Detail: string
  Source: `atg.catalogue-aggregator.${EventSource}`
}

const PAGE_LIMIT = 1_000

const getTableName = () => process.env.OUTBOX_TABLE_NAME

export const FALLBACK_TIMESTAMP = new Date(0).toISOString()

export class Outbox {
  transformRaw(this: void, record: Record<string, AttributeValue>) {
    return unmarshall(record) as OutboxDbRecord
  }

  async write(records: Record<string, unknown>[]): Promise<void> {
    if (!records.length) return

    for (const batch of chunk(records, 25)) {
      let pending: WriteRequest[] = batch.map((event) => ({
        PutRequest: { Item: marshall(event) },
      }))

      while (pending.length) {
        const res = await ddbCall(
          'batchwrite',
          ddbClient.send(
            new BatchWriteItemCommand({
              RequestItems: { [getTableName()]: pending },
              ReturnConsumedCapacity: 'TOTAL',
            })
          ),
          { entity: 'outbox', table: getTableName(), attempted: batch.length }
        )

        pending = res.UnprocessedItems?.[getTableName()] ?? []
        if (pending.length) await sleep(200)
      }
    }
  }

  private async wasSuperseded(this: void, { pk, createdAt }: OutboxDbRecord) {
    const params: QueryCommandInput = {
      TableName: getTableName(),
      KeyConditionExpression: 'pk = :pk',
      FilterExpression:
        'attribute_exists(emittedAt) AND createdAt > :createdAt',
      ExpressionAttributeValues: {
        ':pk': { S: pk },
        ':createdAt': { S: createdAt },
      },
      Limit: 1,
      ReturnConsumedCapacity: 'TOTAL',
    }

    const { Items } = await ddbCall(
      'query',
      ddbClient.send(new QueryCommand(params)),
      { entity: 'outbox', table: getTableName() }
    )

    return Boolean(Items?.length)
  }

  async fetchUniqUnprocessed(): Promise<OutboxDbRecord[]> {
    const dedupe = new Map<string, OutboxDbRecord>()

    const stale: OutboxDbRecord[] = []

    let lastEvaluatedKey: Record<string, AttributeValue> | undefined

    do {
      const params: QueryCommandInput = {
        TableName: getTableName(),
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :pending',
        ExpressionAttributeValues: {
          ':pending': { S: 'PENDING' },
        },
        Limit: PAGE_LIMIT,
        ReturnConsumedCapacity: 'TOTAL',
        ...(lastEvaluatedKey && { ExclusiveStartKey: lastEvaluatedKey }),
      }

      const response = await ddbCall(
        'query',
        ddbClient.send(new QueryCommand(params)),
        { entity: 'outbox', table: getTableName() }
      )

      for (const item of response.Items ?? []) {
        const transformed = this.transformRaw(item)
        const existing = dedupe.get(transformed.pk)

        if (!existing || transformed.createdAt > existing.createdAt) {
          // eslint-disable-next-line @typescript-eslint/no-unused-expressions
          existing && stale.push(existing)
          dedupe.set(transformed.pk, transformed)

          continue
        }

        stale.push(transformed)
      }

      lastEvaluatedKey = response.LastEvaluatedKey
    } while (lastEvaluatedKey)

    const validUnprocessedRecords = []

    for (const record of dedupe.values()) {
      const superseded = await this.wasSuperseded(record)

      if (superseded) {
        stale.push(record)
        continue
      }

      validUnprocessedRecords.push(record)
    }

    await Promise.all(
      stale.map((item) => this.markEmission(item, FALLBACK_TIMESTAMP))
    )

    return validUnprocessedRecords
  }

  async markEmission(
    this: void,
    { pk, sk, attempts }: OutboxDbRecord,
    timestamp: string
  ): Promise<void> {
    const ttlTwoHours = Math.floor(Date.now() / 1000) + 2 * 60 * 60

    try {
      await ddbCall(
        'update',
        ddbClient.send(
          new UpdateItemCommand({
            TableName: getTableName(),
            Key: marshall({ pk, sk }),
            UpdateExpression:
              'SET emittedAt = :emittedAt, expiresAt = :expiresAt, attempts = :attempts REMOVE GSI1PK',
            ConditionExpression:
              'attribute_not_exists(emittedAt) AND GSI1PK = :pending',
            ExpressionAttributeValues: marshall({
              ':emittedAt': timestamp,
              ':expiresAt': ttlTwoHours,
              ':attempts': (attempts ?? 0) + 1,
              ':pending': 'PENDING',
            }),
            ReturnConsumedCapacity: 'TOTAL',
          })
        ),
        { entity: 'outbox', table: getTableName() }
      )
    } catch (error) {
      if (error instanceof ConditionalCheckFailedException) return

      throw new Error(
        `Failed to update outbox record pk=${pk}, sk=${sk}: ${error as Error}`
      )
    }
  }
}
