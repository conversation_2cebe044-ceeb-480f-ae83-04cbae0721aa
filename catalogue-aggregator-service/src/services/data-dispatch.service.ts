/*───────────────────────────────────────────────────────────────────────────*\
  services/data-dispatch.service.ts
  – Sends docs to Typesense, records aggregated metrics, and writes
    successful “publish” events to the Outbox for async fan-out.
  – Dual-write: if TYPESENSE_CLUSTER_SECONDARY is set for the stage,
    writes go to both clusters (primary awaited, secondary best-effort).
\*───────────────────────────────────────────────────────────────────────────*/

import zlib from 'zlib'
import { promisify } from 'util'

import { v4 as uuidV4 } from 'uuid'
import { serverLogger, type Logger } from '@atg-digital/server-logger-library'

import { metric } from '../utils/metric'
import { En<PERSON><PERSON>, Layer, Op, Result } from '../utils/metric-vocabulary'
import { Typesense } from '../clients/typesense'
import { Outbox } from '../clients/outbox'
import { EventBus } from '../clients/eventbus'
import { normalizeError } from '../utils/common'

import type { Item, ItemDispatchMap } from '../interfaces'
import type { Collection } from '../typesense/interfaces'
import type { DeleteSummary, ImportSummary } from '../clients/typesense'

/*────────────────────────── types ──────────────────────────*/
export interface EntityOps<I extends Item = Item> {
  alias: Collection
  imports?: I[]
  deletes?: I[]
}

interface Plan {
  alias: Collection
  op: Op
  count: number
  promise: Promise<ImportSummary | DeleteSummary>
}

interface RespSuccess {
  status: 'fulfilled'
  value: ImportSummary | DeleteSummary
}

export type Response = RespSuccess | { status: 'rejected'; reason: unknown }

export type EventSource = 'production'

/*───────────────── helper: latency wrapper ─────────────────*/
async function withTiming<T>(
  p: Promise<T>,
  dims: { alias: Collection; op: Op }
): Promise<T> {
  const t0 = Date.now()
  try {
    return await p
  } finally {
    metric('ts_op_latency_ms', Date.now() - t0, {
      layer: Layer.TYPESENSE,
      entity: dims.alias,
      op: dims.op,
    })
  }
}

/*──────────────────────── service ─────────────────────────*/
export class DataDispatchService {
  private constructor(
    private typesense: Typesense,
    private outbox: Outbox,
    private logger: Logger,
    /** present only if TYPESENSE_CLUSTER_SECONDARY is set */
    private secondary?: Typesense
  ) {}

  static async init(logger: Logger, indexMode: 'bulk' | 'normal' = 'normal') {
    const primary = await Typesense.init({ mode: indexMode })
    const outbox = new Outbox()

    const secondarySlug = process.env.TYPESENSE_CLUSTER_SECONDARY?.trim()
    let secondary: Typesense | undefined

    if (secondarySlug) {
      secondary = await Typesense.initForCluster(secondarySlug, {
        mode: indexMode,
      })
      logger.info(
        { secondarySlug },
        'Typesense dual-write enabled (secondary cluster configured)'
      )
    } else {
      logger.info('Typesense dual-write not configured (single cluster)')
    }

    return new DataDispatchService(primary, outbox, logger, secondary)
  }

  /* only “show” imports get re-emitted to the EventBus */
  private get eventDispatchCollections(): (keyof ItemDispatchMap)[] {
    return ['show']
  }

  private get collectionToEventSourceMap(): Record<
    keyof ItemDispatchMap,
    EventSource
  > {
    return {
      show: 'production',
    } as const
  }

  /*─────────────────────── emit ───────────────────────*/
  public async emit(ops: EntityOps[]): Promise<void> {
    try {
      /* 1️⃣ build execution plan */
      const plan = this.plan(ops)

      /* 2️⃣ run tasks */
      const responses = (await Promise.allSettled(
        plan.map((p) => p.promise)
      )) as Response[]

      /* 3️⃣ normalise success / failure counts */
      for (const res of responses) {
        if (res.status !== 'fulfilled') {
          // full batch failure
          const selectedPlan = plan[responses.indexOf(res)]
          if (!selectedPlan) continue

          const { alias, op, count } = selectedPlan
          metric('ts_op_count', count, {
            layer: Layer.TYPESENSE,
            entity: alias,
            op,
            result: Result.FAILED,
          })
          continue
        }

        const { value } = res
        const op = value.op === 'imports' ? Op.IMPORT : Op.DELETE
        const succ = value.success?.count ?? 0
        const failed = value.failed?.count ?? 0

        if (succ)
          metric('ts_op_count', succ, {
            layer: Layer.TYPESENSE,
            entity: value.collection,
            op,
            result: Result.SUCCESS,
          })

        if (failed)
          metric('ts_op_count', failed, {
            layer: Layer.TYPESENSE,
            entity: value.collection,
            op,
            result: Result.FAILED,
          })
      }

      /* 4️⃣ create & save Outbox rows for successful *imports* */
      const outboxRecs = await this.buildOutboxRecords(ops, responses)

      const tOb = Date.now()
      try {
        await this.outbox.write(outboxRecs)

        metric('outbox_write_latency_ms', Date.now() - tOb, {
          layer: Layer.DB,
          entity: Entity.OUTBOX,
          op: Op.PUT,
          result: Result.SUCCESS,
        })
        if (outboxRecs.length) {
          metric('outbox_write_count', outboxRecs.length, {
            layer: Layer.DB,
            entity: Entity.OUTBOX,
            op: Op.PUT,
            result: Result.SUCCESS,
          })
        }
      } catch (e) {
        metric('outbox_write_latency_ms', Date.now() - tOb, {
          layer: Layer.DB,
          entity: Entity.OUTBOX,
          op: Op.PUT,
          result: Result.FAILED,
        })
        metric('outbox_write_error_count', 1, {
          layer: Layer.DB,
          entity: Entity.OUTBOX,
          op: Op.PUT,
          result: Result.FAILED,
        })
        throw e
      }
    } catch (err) {
      const norm = normalizeError(err)
      this.logger.error(norm, 'Dispatch emission failed')
      throw err
    }
  }

  private async formatOutbox<K extends keyof ItemDispatchMap>(
    item: ItemDispatchMap[K],
    collection: K
  ) {
    const eventId = uuidV4()

    const payload = JSON.stringify(
      EventBus.createEventPayload({
        detailType: 'publish',
        eventBusName: process.env.SHARED_CATALOGUE_EVENTBUS_ARN,
        event: {
          metadata: {
            traceContext: serverLogger.traceContext,
            service: 'catalogue-aggregator',
            id: eventId,
          },
          data: item.toEventDetail(),
        },
        source: `atg.catalogue-aggregator.${this.collectionToEventSourceMap[collection]}`,
      })
    )

    const gzipAsync = promisify(zlib.gzip)

    const buffer = await gzipAsync(Buffer.from(payload, 'utf-8'))

    return {
      pk: item.pk,
      sk: eventId,
      createdAt: new Date().toISOString(),
      payload: buffer,
      GSI1PK: 'PENDING',
    }
  }

  /*──────────────────────── helpers ───────────────────────*/

  /** Fire-and-forget mirror to secondary; primary is awaited elsewhere */
  private mirrorSecondary<T>(run: () => Promise<T>, ctx: object) {
    if (!this.secondary) return
    run().catch((err) => {
      const error = normalizeError(err)
      this.logger.error({ error, ...ctx }, 'Secondary Typesense op failed')
    })
  }

  private importDual(
    alias: Collection,
    docs: ReturnType<Item['toDocument']>[],
    log: Logger
  ): Promise<ImportSummary> {
    // Primary is the SLO’d/awaited call
    const primary = this.typesense.importDocuments(alias, docs, log)
    // Secondary (if configured) best-effort
    this.mirrorSecondary(
      () => this.secondary!.importDocuments(alias, docs, log),
      { alias, count: docs.length, op: 'import' }
    )
    return primary
  }

  private deleteDual(
    alias: Collection,
    ids: string[],
    log: Logger
  ): Promise<DeleteSummary> {
    const primary = this.typesense.deleteDocumentsById(alias, ids, log)
    this.mirrorSecondary(
      () => this.secondary!.deleteDocumentsById(alias, ids, log),
      { alias, count: ids.length, op: 'delete' }
    )
    return primary
  }

  private plan(entities: EntityOps[]): Plan[] {
    const log = this.logger.child({ phase: 'typesense-batch' })
    const plan: Plan[] = []

    for (const { alias, imports, deletes } of entities) {
      if (imports?.length) {
        const docs = imports.map((i) => i.toDocument())
        const p = this.importDual(
          alias,
          docs,
          log.child({ alias, op: 'import', count: docs.length })
        )
        plan.push({
          alias,
          op: Op.IMPORT,
          count: docs.length,
          promise: withTiming(p, { alias, op: Op.IMPORT }),
        })
      }

      if (deletes?.length) {
        const ids = deletes.map((d) => d.id)
        const p = this.deleteDual(
          alias,
          ids,
          log.child({ alias, op: 'delete', count: ids.length })
        )
        plan.push({
          alias,
          op: Op.DELETE,
          count: ids.length,
          promise: withTiming(p, { alias, op: Op.DELETE }),
        })
      }
    }

    return plan
  }

  private async buildOutboxRecords(ops: EntityOps[], res: Response[]) {
    const records = this.eventDispatchCollections.flatMap((collection) => {
      const opCfg = ops.find((o) => o.alias === collection)
      const r = res.find(
        (x): x is RespSuccess =>
          x.status === 'fulfilled' && x.value.collection === collection
      )

      if (!r?.value.success?.count) return []

      const okIds = new Set(r.value.success.ids)
      const toSend = (opCfg?.imports ?? []).filter(
        (i): i is ItemDispatchMap[typeof collection] => okIds.has(i.id)
      )

      return toSend.map((i) => this.formatOutbox(i, collection))
    })

    return Promise.all(records)
  }
}
