import { utc, type Dayjs } from 'dayjs'

import { PERFORMANCE_STATUS_FIELDS } from '../utils/constants'
import { pick } from '../utils/common'
import { now, toUtc, dateCompare } from '../utils/dates'
import { Status } from '../interfaces'

import { getPerformanceStatus } from './performance-status'

import type { OnSaleRangeDate } from '@atg-digital/cms-types'
import type { AggregatedFields } from '../interfaces'
import type { PerformanceItem } from '../models/inventory/performance'
import type { ProductionItem } from '../models'

const STATUS_LADDER: readonly Status[] = [
  Status.GENERAL_ON_SALE,
  Status.PRE_SALE,
  Status.TEASER,
  Status.SOLD_OUT,
  Status.EMBARGO,
  Status.POSTPONED,
  Status.CANCELLED,
  Status.COMING_SOON,
] as const

export class StatusService {
  static statusForPerformance(
    performance: PerformanceItem,
    offsetMinutes = 0
  ): Status {
    const slice = pick(performance, [...PERFORMANCE_STATUS_FIELDS])

    return getPerformanceStatus(slice, offsetMinutes)
  }

  /*─────────────────── 2 ▶ PRODUCTION ────────────────────*/

  /**
   * Computes the headline status for **one** production page.
   *
   * @param production               raw CMS row
   * @param aggregatedPerformances   roll-up of its performances
   */
  static statusForProduction(
    production: ProductionItem,
    aggregatedPerformances: AggregatedFields,
    offsetMinutes = 0
  ): Status {
    // ➊ Allow CMS flags to override everything.
    const override = this.#cmsOverrideStatus(production, offsetMinutes)
    if (override) return override

    // ➋ Fallback to the ladder-based decision from its performances.
    return this.decideStatus(aggregatedPerformances, offsetMinutes)
  }

  /*──────────────────── 3 ▶  TOUR  ───────────────────────*/

  static statusForTour(
    aggregatedProductions: AggregatedFields,
    offsetMinutes = 0
  ): Status {
    // The tour already contains a merged `status` set from its shows.
    return this.decideStatus(aggregatedProductions, offsetMinutes)
  }

  /*─────────────────── 4 ▶  FUTURE MOMENTS ───────────────*/

  /** UTC instants after which a production status *could* change. */
  static getProductionStatusChangeMoments(prod: ProductionItem): Dayjs[] {
    const isoStamps: (string | undefined)[] = [
      // Every configured on-sale window.
      ...(prod.details.onSaleDates?.map((w) => w.onSaleDate) ?? []),
      // Embargo-lift moment (if any).
      prod.details.embargoDate,
    ]
    if (!prod.timezone) {
      throw new Error(
        `Production ${prod.showSlug} & ${prod.venueSlug} for ${prod.brandAlias} has no timezone set.`
      )
    }
    return this.#distinctFutureMoments(isoStamps, prod.timezone)
  }

  /*──────────────────── PRIVATE HELPERS ───────────────────*/

  /*───────── 4.1  CMS-override ladder (production only) ─────────*/

  static #cmsOverrideStatus(
    prod: ProductionItem,
    offset: number
  ): Status | undefined {
    /* ── Time references ──────────────────────────────────────*/
    const utcNow = now(offset)
    const embargoEnds = toUtc(prod.details.embargoDate, prod.timezone)

    /* ── Convenience aliases ─────────────────────────────────*/
    const { specialEventType, forcePostponedOrCanceled } = prod.settings
    const { isSoldOut, externalURL, onSaleDates } = prod.details

    const hasNoOnSaleDates = !onSaleDates?.length
    const isCancelled = forcePostponedOrCanceled === 'Canceled'
    const isPostponed = forcePostponedOrCanceled === 'Postponed'
    const inEmbargoPeriod = dateCompare(utcNow, embargoEnds, 'before')

    /* ── Top-to-bottom ladder (first match wins) ─────────────*/
    const ladder: [boolean, Status][] = [
      [inEmbargoPeriod, Status.EMBARGO],
      [specialEventType === 'free', Status.FREE],
      [specialEventType === 'private', Status.PRIVATE],
      [isSoldOut, Status.SOLD_OUT],
      [isCancelled, Status.CANCELLED],
      [isPostponed, Status.POSTPONED],
      [
        Boolean(externalURL) && prod.sourceType === 'contentful',
        Status.GENERAL_ON_SALE,
      ],
      [prod.brandAlias !== 'atgtk' && hasNoOnSaleDates, Status.COMING_SOON],
    ]

    for (const [match, result] of ladder) if (match) return result

    // Nothing matched → evaluate purely from on-sale windows.
    return this.statusFromOnSaleWindows(prod, utcNow)
  }

  /*───────── 4.2  Date-window logic (production only) ────────*/

  static statusFromOnSaleWindows(
    prod: ProductionItem,
    utcNow: Dayjs
  ): Status | undefined {
    const saleWindows = prod.details.onSaleDates
    if (!saleWindows?.length) return undefined

    const isActive = (w: OnSaleRangeDate) => {
      if (w.isSoldOut) return false
      const start = toUtc(w.onSaleDate, prod.timezone)
      const end = toUtc(w.offSaleDate, prod.timezone)

      // A window is active if it has started (no start or start <= now)
      // AND hasn't ended (no end or end > now).
      const started = !start.isValid() || !start.isAfter(utcNow)
      const notEnded = !end.isValid() || end.isAfter(utcNow)
      return started && notEnded
    }

    const active = saleWindows.filter(isActive)

    const anyActiveGeneral = active.some((w) => w.onSaleType === 'General')
    const anyActivePresale = active.some((w) => w.onSaleType !== 'General')

    const anyFuture = saleWindows.some((w) => {
      const start = toUtc(w.onSaleDate, prod.timezone)
      return start.isValid() && start.isAfter(utcNow)
    })

    if (anyActiveGeneral) {
      const manualBuyLink =
        Boolean(prod.settings.overrides?.buyTicketsUrl) ||
        Boolean(prod.details.externalURL)
      if (manualBuyLink) return Status.GENERAL_ON_SALE
      return
    }

    if (anyActivePresale) return Status.PRE_SALE
    if (anyFuture) return Status.TEASER
    return Status.CLOSED
  }

  /*───────── 4.3  Date utilities ────────────────────────────*/

  static #distinctFutureMoments(
    isoStamps: (string | undefined)[],
    tz: string
  ): Dayjs[] {
    const ref = now()
    const msec = new Set<number>()

    for (const iso of isoStamps) {
      const ts = toUtc(iso, tz)
      if (ts.isValid() && ts.isAfter(ref)) msec.add(ts.valueOf())
    }
    return [...msec].sort((a, b) => a - b).map((ms) => utc(ms))
  }

  /*───────── 4.4  Final ladder reducer used by higher levels ─*/

  static decideStatus(
    aggregatedPerformances: AggregatedFields,
    offset = 0
  ): Status {
    /* ➊  Pick the first status present on the global ladder. */
    const top = STATUS_LADDER.find((s) =>
      (aggregatedPerformances.status as Set<Status>).has(s)
    )
    if (top) return top

    /* ➋  No ladder hit – fallback: “run still in progress?”   */
    const lastUtc = toUtc(aggregatedPerformances.lastPerformanceDate)
    const runStillActive = lastUtc.isValid() && now(offset).isBefore(lastUtc)

    return runStillActive ? Status.GENERAL_ON_SALE : Status.CLOSED
  }
}
