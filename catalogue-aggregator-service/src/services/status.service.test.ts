// production-status.spec.ts
import dayjs, { extend } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import tz from 'dayjs/plugin/timezone'
import customParse from 'dayjs/plugin/customParseFormat'

import { Status } from '../interfaces'

import { StatusService } from './status.service'

import type { OnSaleRangeDate } from '@atg-digital/cms-types'
import type { ProductionItem } from '../models'
import type { AggregatedFields } from '../interfaces'

extend(utc)
extend(tz)
extend(customParse)

const FIXED_NOW = dayjs.utc('2025-01-15T12:00:00Z')

// Small helpers
const win = (partial: Partial<OnSaleRangeDate>): OnSaleRangeDate => ({
  contentTypeAlias: 'onSaleDate',
  description: '',
  isSoldOut: false,
  onSaleType: 'Promo/Access',
  hideDateAndTime: false,
  ...partial,
})

const buildProd = (overrides: Partial<ProductionItem> = {}) =>
  ({
    timezone: 'UTC',
    details: { onSaleDates: [], ...overrides.details },
    settings: { overrides: {}, ...overrides.settings },
    performancesIds: overrides.performancesIds,
  }) as ProductionItem

describe('statusFromOnSaleWindows', () => {
  it('returns undefined when there are no onSale windows', () => {
    const prod = buildProd({
      details: { onSaleDates: [] },
    } as unknown as Partial<ProductionItem>)

    expect(
      StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)
    ).toBeUndefined()
  })

  it('future-only windows → TEASER', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({ onSaleType: 'General', onSaleDate: '2025-02-01T00:00:00Z' }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.TEASER
    )
  })

  it('active Presale (only) → PRE_SALE', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-10T00:00:00Z',
            offSaleDate: '2025-02-01T00:00:00Z',
          }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.PRE_SALE
    )
  })

  describe('active General → gate on manual link (perf IDs are ignored)', () => {
    const generalActive = win({
      onSaleType: 'General',
      onSaleDate: '2025-01-01T00:00:00Z',
    })

    it.each`
      buyTicketsUrl                | externalURL         | expected
      ${'https://buy.example.com'} | ${undefined}        | ${Status.GENERAL_ON_SALE}
      ${undefined}                 | ${'https://ext.io'} | ${Status.GENERAL_ON_SALE}
      ${'https://buy.example.com'} | ${'https://ext.io'} | ${Status.GENERAL_ON_SALE}
      ${undefined}                 | ${undefined}        | ${undefined}
    `(
      'General active permutations: buy=$buyTicketsUrl, ext=$externalURL → $expected',
      ({
        buyTicketsUrl,
        externalURL,
        expected,
      }: {
        buyTicketsUrl: string | undefined
        externalURL: string | undefined
        expected: Status | undefined
      }) => {
        const prod = buildProd({
          details: { onSaleDates: [generalActive], externalURL },
          settings: { overrides: { buyTicketsUrl } },
          // performancesIds presence is intentionally irrelevant now
          performancesIds: [],
        } as unknown as Partial<ProductionItem>)

        expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
          expected
        )
      }
    )
  })

  it('General with no onSaleDate (missing) counts as active immediately (needs manual link to surface GENERAL_ON_SALE)', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [win({ onSaleType: 'General', onSaleDate: undefined })],
      },
      settings: { overrides: { buyTicketsUrl: 'https://buy.me' } },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.GENERAL_ON_SALE
    )
  })

  it('General ended (offSaleDate == now) is NOT active → CLOSED when nothing else', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'General',
            onSaleDate: '2025-01-01T00:00:00Z',
            offSaleDate: '2025-01-15T12:00:00Z', // equals FIXED_NOW → ended
          }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.CLOSED
    )
  })

  it('start boundary: onSaleDate == now → active (manual link required for GENERAL_ON_SALE)', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({ onSaleType: 'General', onSaleDate: '2025-01-15T12:00:00Z' }),
        ],
        externalURL: 'https://ext.io', // makes it GENERAL_ON_SALE
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.GENERAL_ON_SALE
    )
  })

  it('end boundary: offSaleDate == now → not active; with future presale → TEASER', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-01T00:00:00Z',
            offSaleDate: '2025-01-15T12:00:00Z',
          }),
          win({ onSaleType: 'General', onSaleDate: '2025-02-01T00:00:00Z' }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.TEASER
    )
  })

  it('active Presale + future General → PRE_SALE (active wins over future)', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-01T00:00:00Z',
            offSaleDate: '2025-02-01T00:00:00Z',
          }),
          win({ onSaleType: 'General', onSaleDate: '2025-02-10T00:00:00Z' }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.PRE_SALE
    )
  })

  it('active General + active Presale → GENERAL_ON_SALE only if manual link present; otherwise defer (undefined)', () => {
    const withLink = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-10T00:00:00Z',
            offSaleDate: '2025-02-01T00:00:00Z',
          }),
          win({
            onSaleType: 'General',
            onSaleDate: '2025-01-01T00:00:00Z',
            offSaleDate: '2025-02-01T00:00:00Z',
          }),
        ],
        externalURL: 'https://ext.io',
      },
    } as unknown as Partial<ProductionItem>)

    const noLink = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-10T00:00:00Z',
            offSaleDate: '2025-02-01T00:00:00Z',
          }),
          win({
            onSaleType: 'General',
            onSaleDate: '2025-01-01T00:00:00Z',
            offSaleDate: '2025-02-01T00:00:00Z',
          }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(withLink, FIXED_NOW)).toBe(
      Status.GENERAL_ON_SALE
    )
    expect(
      StatusService.statusFromOnSaleWindows(noLink, FIXED_NOW)
    ).toBeUndefined()
  })

  it('no active windows and no future windows → CLOSED (all past)', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2024-12-01T00:00:00Z',
            offSaleDate: '2024-12-15T00:00:00Z',
          }),
          win({
            onSaleType: 'General',
            onSaleDate: '2024-12-05T00:00:00Z',
            offSaleDate: '2024-12-31T23:59:59Z',
          }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.CLOSED
    )
  })

  it('invalid onSaleDate → treated as “no start” → active presale only ⇒ PRE_SALE', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({ onSaleType: 'Promo/Access', onSaleDate: 'not-a-date' }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.PRE_SALE
    )
  })

  it('invalid offSaleDate → treated as “no end” → active presale', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-01T00:00:00Z',
            offSaleDate: 'bad',
          }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.PRE_SALE
    )
  })

  it('timezone parsing (naive local time): Chicago 2025-01-15 08:00:00 local < 13:00Z now ⇒ active presale', () => {
    const localStart = '2025-01-15 08:00:00' // America/Chicago naive
    const prod: ProductionItem = {
      timezone: 'America/Chicago',
      details: {
        onSaleDates: [
          win({ onSaleType: 'Promo/Access', onSaleDate: localStart }),
        ],
      },
      settings: {},
      performancesIds: [],
    } as unknown as ProductionItem
    // FIXED_NOW 13:00Z == 07:00 Chicago CST; localStart 08:00 Chicago → 14:00Z
    // start 14:00Z isAfter(13:00Z) => future ⇒ not active; but we add a prior presale to make it active:
    prod.details.onSaleDates!.push(
      win({ onSaleType: 'Promo/Access', onSaleDate: '2025-01-10T00:00:00Z' })
    )

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.PRE_SALE
    )
  })

  it('General active but NO manual link (even if a presale is also active) → undefined (defer to aggregated)', () => {
    const prod = buildProd({
      details: {
        externalURL: undefined,
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-10T00:00:00Z',
          }),
          win({ onSaleType: 'General', onSaleDate: '2025-01-01T00:00:00Z' }),
        ],
      },
      settings: { overrides: { buyTicketsUrl: undefined } },
    } as unknown as Partial<ProductionItem>)

    expect(
      StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)
    ).toBeUndefined()
  })

  it('active Presale but sold out → NOT active; with future General → TEASER', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-01T00:00:00Z',
            isSoldOut: true,
          }),
          win({ onSaleType: 'General', onSaleDate: '2025-02-10T00:00:00Z' }), // future exists → TEASER
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.TEASER
    )
  })

  it('active General but sold out → NOT active; no other active or future → CLOSED', () => {
    const prod = buildProd({
      details: {
        onSaleDates: [
          win({
            onSaleType: 'General',
            onSaleDate: '2025-01-01T00:00:00Z',
            isSoldOut: true,
          }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.CLOSED
    )
  })

  it('sold-out Presale + active General (manual link) → GENERAL_ON_SALE', () => {
    const prod = buildProd({
      settings: { overrides: { buyTicketsUrl: 'https://buy.me' } },
      details: {
        onSaleDates: [
          win({
            onSaleType: 'Promo/Access',
            onSaleDate: '2025-01-01T00:00:00Z',
            isSoldOut: true,
          }),
          win({ onSaleType: 'General', onSaleDate: '2025-01-05T00:00:00Z' }),
        ],
      },
    } as unknown as Partial<ProductionItem>)

    expect(StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)).toBe(
      Status.GENERAL_ON_SALE
    )
  })

  it('sold-out Presale + active General (no manual link) → undefined (defer)', () => {
    const prod = buildProd({
      details: {
        externalURL: undefined,
        onSaleDates: [
          win({
            onSaleType: 'Member',
            onSaleDate: '2025-01-01T00:00:00Z',
            isSoldOut: true,
          }),
          win({ onSaleType: 'General', onSaleDate: '2025-01-05T00:00:00Z' }),
        ],
      },
      settings: { overrides: { buyTicketsUrl: undefined } },
    } as unknown as Partial<ProductionItem>)

    expect(
      StatusService.statusFromOnSaleWindows(prod, FIXED_NOW)
    ).toBeUndefined()
  })
})

describe('statusForProduction (integration)', () => {
  it('General active but no manual link → falls back to aggregated status', () => {
    // Make "General" active regardless of clock by omitting start date.
    const prod = buildProd({
      details: {
        onSaleDates: [win({ onSaleType: 'General', onSaleDate: undefined })],
        externalURL: undefined,
      },
      settings: { overrides: { buyTicketsUrl: undefined } },
    } as unknown as Partial<ProductionItem>)

    const aggregated = {
      status: new Set<Status>([Status.PRE_SALE]),
      lastPerformanceDate: '2025-01-20T00:00:00Z',
    } as unknown as AggregatedFields

    expect(StatusService.statusForProduction(prod, aggregated)).toBe(
      Status.PRE_SALE
    )
  })
})
