/*───────────────────────────────────────────────────────────────────────────*\
  inventory/performance-stream-batch.ts
  – Fan-out perf changes, wait-list cleanup, batch Typesense ops
\*───────────────────────────────────────────────────────────────────────────*/
import {
  type AttributeValue,
  BatchWriteItemCommand,
  QueryCommand,
  type WriteRequest,
} from '@aws-sdk/client-dynamodb'

import { loggerForStream } from '../../utils/logger'
import { flushMetrics, metric, setMetricLogger } from '../../utils/metric'
import { PerformanceItem, ProductionItem } from '../../models'
import { alertSlack } from '../../utils/alert-slack'
import { chunk, normalizeError, sleep } from '../../utils/common'
import { ddbClient } from '../../clients/ddb'
import { buildGSIQuery } from '../../utils/ddb'
import { traceToMessageAttributes } from '../../utils/trace'
import { auditPerf } from '../../utils/audit'
import {
  Action,
  Entity,
  Layer,
  Op,
  Result,
} from '../../utils/metric-vocabulary'
import { Status } from '../../interfaces'
import { DataDispatchService } from '../../services/data-dispatch.service'
import { splitItemsByStatus } from '../../utils/typesense'
import { ddbCall } from '../../utils/ddb-metric-wrapper'
import { InstrumentedSQS } from '../../clients/sqs'

import type {
  Context,
  DynamoDBStreamEvent,
  DynamoDBStreamHandler,
} from 'aws-lambda'
import type { Logger } from '@atg-digital/server-logger-library'

/*──────────────────────── constants ────────────────────────*/
const QUEUE_URL = process.env.SQS_FIFO_URL
const TABLE_NAME = process.env.TABLE_NAME
const HANDLER = 'inventory/performance-stream-batch'

export const handler: DynamoDBStreamHandler = async (
  event: DynamoDBStreamEvent,
  ctx: Context
) => {
  const log = loggerForStream(event, ctx, HANDLER)
  const sqs = new InstrumentedSQS({ logger: log })
  setMetricLogger(log)
  log.info({ records: event.Records.length }, 'Stream batch start')

  /* counters (for billboards & alerts) */
  const stats = {
    processed: 0,
    invalid: 0,
    waitlistDeleted: 0,
    waitlistErrors: 0,
    flipAttempt: 0,
    flipSuccess: 0,
  }

  const performanceImports: PerformanceItem[] = []
  const performanceDeletes: PerformanceItem[] = []
  const productionDeletes: ProductionItem[] = []

  /** dedupe key → aggregate job payload  */
  const dedupe: Record<
    string,
    {
      venueSlug: string
      showSlug: string
      tenantCode: string
      waitlist?: boolean
    }
  > = {}

  try {
    /*───────────────── 1) consume stream records ─────────────────*/
    for (const rec of event.Records) {
      const tRec = Date.now()
      stats.processed++

      // Per-record freshness from DynamoDB Streams
      const acdt = rec.dynamodb?.ApproximateCreationDateTime
      if (acdt) {
        metric('event_lag_ms', Date.now() - Math.floor(acdt * 1000), {
          layer: Layer.LAMBDA,
          entity: Entity.PERFORMANCE,
          action: Action.LAG,
        })
      }

      try {
        const newImg = rec.dynamodb?.NewImage
        if (!newImg) {
          stats.invalid++
          log.warn(
            {
              eventName: rec.eventName,
              keys: rec.dynamodb?.Keys,
            },
            'No NewImage on stream record – skipped'
          )
          continue
        }

        // Build typed Performance objects from raw AttributeValue maps
        let nextPerf: PerformanceItem
        let prevPerf: PerformanceItem | undefined
        try {
          nextPerf = PerformanceItem.fromDbItem(
            newImg as Record<string, AttributeValue>
          )
          if (rec.dynamodb?.OldImage) {
            prevPerf = PerformanceItem.fromDbItem(
              rec.dynamodb.OldImage as Record<string, AttributeValue>
            )
          }
        } catch (err) {
          stats.invalid++
          log.warn({ err }, 'Unmarshall PerformanceItem failed')
          await alertSlack({
            handler: HANDLER,
            error: normalizeError(err).error,
            detail: { record: rec },
          })
          metric('lambda_record_failure_count', 1, {
            layer: Layer.LAMBDA,
            entity: Entity.PERFORMANCE,
            result: Result.FAILED,
          })
          continue
        }

        // Audit diff (only if something changed)
        const diff = PerformanceItem.diff(prevPerf, nextPerf)

        if (Object.keys(diff).length) auditPerf(log, nextPerf, diff)

        // Performance validation
        productionDeletes.push(
          ...(await productionDeleteIfInvalidPerformance(nextPerf, diff, log))
        )

        // classify for Typesense sync
        const { imports, deletes } = splitItemsByStatus([nextPerf])
        performanceImports.push(...imports)
        performanceDeletes.push(...deletes)

        // dedupe for SQS fan-out + waitlist flip detection
        const key = `${nextPerf.venueSlug}#${nextPerf.showSlug}#${nextPerf.tenantCode}`
        dedupe[key] ??= {
          venueSlug: nextPerf.venueSlug,
          showSlug: nextPerf.showSlug,
          tenantCode: nextPerf.tenantCode,
          // mark waitlist flip only when a new record is inserted and NOT already WAITLIST
          waitlist:
            rec.eventName === 'INSERT' &&
            nextPerf.overrideStatus !== 'WAITLIST',
        }
      } catch (err) {
        const { error, message, stackLines } = normalizeError(err)
        stats.invalid++
        log.error({ error, message, stackLines }, 'Record processing failed')
        await alertSlack({
          handler: HANDLER,
          error: normalizeError(err).error,
          detail: { record: rec },
        })
        metric('lambda_record_failure_count', 1, {
          layer: Layer.LAMBDA,
          entity: Entity.PERFORMANCE,
          result: Result.FAILED,
        })
      } finally {
        // Per-record processing time
        metric('lambda_record_latency_ms', Date.now() - tRec, {
          layer: Layer.LAMBDA,
        })
      }
    }

    /*───────────────── 2) wait-list cleanup + enqueue jobs ───────*/
    for (const [key, payload] of Object.entries(dedupe)) {
      // wait-list flip logic
      if (payload.waitlist) {
        stats.flipAttempt++
        const res = await handleWaitlistCleanup(payload, log)
        stats.waitlistDeleted += res.deleted
        stats.waitlistErrors += res.errors
        performanceDeletes.push(...res.performanceItems)
        if (res.deleted) stats.flipSuccess++
      }
      const dedupId = `${key}-${Math.floor(Date.now() / 10_000)}`

      await sqs.sendMessage(
        {
          queueUrl: QUEUE_URL,
          body: JSON.stringify(payload),
          groupId: key,
          dedupId,
          messageAttributes: traceToMessageAttributes(),
        },
        {
          entity: Entity.QUEUE_PERF_AGG,
        }
      )
    }

    // Wait-list rollups (both success & failure)
    if (stats.waitlistDeleted) {
      metric('waitlist_cleanup_count', stats.waitlistDeleted, {
        layer: Layer.DB,
        entity: Entity.PERFORMANCE,
        op: Op.DELETE,
        result: Result.SUCCESS,
      })
    }
    if (stats.waitlistErrors) {
      metric('waitlist_cleanup_count', stats.waitlistErrors, {
        layer: Layer.DB,
        entity: Entity.PERFORMANCE,
        op: Op.DELETE,
        result: Result.FAILED,
      })
    }

    /*───────────────── 3) bulk Typesense sync ───────────────────*/
    const dispatch = await DataDispatchService.init(log)
    await dispatch.emit([
      {
        alias: 'performance',
        imports: performanceImports,
        deletes: performanceDeletes,
      },
      { alias: 'show', deletes: productionDeletes },
    ])

    log.info(
      {
        upserts: performanceImports.length,
        deletes: performanceDeletes.length,
        waitlistDeleted: stats.waitlistDeleted,
        waitlistErrors: stats.waitlistErrors,
        processed: stats.processed,
        invalid: stats.invalid,
      },
      'Stream batch complete'
    )
  } catch (fatal) {
    const norm = normalizeError(fatal)
    log.error({ ...stats, ...norm }, 'Handler failed')
    await alertSlack({
      handler: HANDLER,
      error: norm.error,
      detail: stats,
      requestId: ctx.awsRequestId,
    })
    throw fatal
  } finally {
    flushMetrics()
  }
}

export async function productionDeleteIfInvalidPerformance(
  performance: PerformanceItem,
  diff: ReturnType<typeof PerformanceItem.diff>,
  logger: Logger
) {
  const { showSlug, venueSlug, venueId, id } = performance

  const { Items: incomingSlugRes } = await ddbCall(
    'query',
    ddbClient.send(
      new QueryCommand(buildGSIQuery(`PRODUCTION#${showSlug}#${venueSlug}`))
    ),
    { entity: Entity.SHOW, table: process.env.TABLE_NAME, access: 'GSI1' }
  )

  if (incomingSlugRes?.length) {
    const production = ProductionItem.fromDbItem(incomingSlugRes[0]!)

    const productionVenueId = production.venue.config?.inventoryId

    if (productionVenueId === venueId) return []

    logger.debug(
      { id, productionVenueId, venueId },
      'Performance venue id does not match production, closing performance'
    )

    Object.assign(performance, { status: Status.CLOSED, isClosed: true })

    await performance.put()

    return []
  }

  if (!diff.showSlug || (diff.showSlug && !diff.showSlug.from)) {
    logger.debug(
      { id, venueSlug, showSlug },
      'No production found for slug, closing performance'
    )

    Object.assign(performance, { status: Status.CLOSED, isClosed: true })

    await performance.put()

    return []
  }

  logger.debug(
    { id, ...diff.showSlug },
    'Show slug changed, checking old production and closing performance'
  )

  const { Items: oldSlugRes } = await ddbCall(
    'query',
    ddbClient.send(
      new QueryCommand(
        buildGSIQuery(`PRODUCTION#${diff.showSlug.from as string}#${venueSlug}`)
      )
    ),
    { entity: Entity.SHOW, table: process.env.TABLE_NAME, access: 'GSI1' }
  )

  Object.assign(performance, { status: Status.CLOSED, isClosed: true })

  await performance.put()

  if (!oldSlugRes?.length) return []

  const production = ProductionItem.fromDbItem(oldSlugRes[0]!)

  production.status = Status.CLOSED

  await production.put()

  logger.info(
    { productionId: production.id, ...diff.showSlug },
    'Production closed due to slug mismatch'
  )

  return [production]
}

/*──────────────────────── WAIT-LIST HELPERS ───────────────────────*/
function buildDeleteRequests(
  rows: Record<string, AttributeValue>[],
  logger: Logger
) {
  const items: PerformanceItem[] = []
  const reqs: WriteRequest[] = rows.map((r) => {
    try {
      items.push(PerformanceItem.fromDbItem(r))
    } catch (err) {
      logger.warn({ err, r }, 'Failed to extract performance ID')
    }
    return { DeleteRequest: { Key: { pk: r.pk! } } }
  })
  return { reqs, items }
}

async function handleWaitlistCleanup(
  perfKey: Pick<PerformanceItem, 'showSlug' | 'venueSlug' | 'tenantCode'>,
  logger: Logger
) {
  const { tenantCode, showSlug, venueSlug } = perfKey
  const gsi1pk = `PERFORMANCE#${tenantCode}#${showSlug}#${venueSlug}`

  let deleted = 0
  let errors = 0
  const performanceItems: PerformanceItem[] = []

  try {
    const query = buildGSIQuery(gsi1pk)
    query.ExpressionAttributeValues = {
      ...query.ExpressionAttributeValues,
      ':WAITLIST': { S: 'WAITLIST' },
    }
    query.FilterExpression = 'overrideStatus = :WAITLIST'

    const { Items = [] } = await ddbCall(
      'query',
      ddbClient.send(new QueryCommand(query)),
      {
        entity: Entity.PERFORMANCE,
        table: TABLE_NAME,
        access: 'GSI1',
      }
    )

    if (!Items.length) return { deleted, errors, performanceItems }

    const { reqs, items } = buildDeleteRequests(Items, logger)
    performanceItems.push(...items)

    for (const batch of chunk(reqs, 25)) {
      let pending = batch
      while (pending.length) {
        const { UnprocessedItems } = await ddbCall(
          'batchwrite',
          ddbClient.send(
            new BatchWriteItemCommand({
              RequestItems: { [TABLE_NAME]: pending },
            })
          ),
          {
            entity: Entity.PERFORMANCE,
            table: TABLE_NAME,
            attempted: batch.length,
          }
        )

        deleted +=
          pending.length - (UnprocessedItems?.[TABLE_NAME]?.length ?? 0)
        pending = UnprocessedItems?.[TABLE_NAME] ?? []
        if (pending.length) await sleep(200)
      }
    }
  } catch (err) {
    errors++
    logger.error({ err, perfKey }, 'WAITLIST cleanup failed')
    await alertSlack({
      handler: HANDLER,
      error: normalizeError(err).error,
      detail: { perfKey, deleted, errors },
    })
  }

  return { deleted, errors, performanceItems }
}
