/*───────────────────────────────────────────────────────────────────────────*\
  inventory/aggregate.ts
  – Groups perf changes → show/tour docs, writes Typesense
\*───────────────────────────────────────────────────────────────────────────*/

import { QueryCommand } from '@aws-sdk/client-dynamodb'

import { ddbClient } from '../../clients/ddb'
import { PerformanceItem, ProductionItem } from '../../models'
import { buildGSIQuery } from '../../utils/ddb'
import { loggerForSQS } from '../../utils/logger'
import { raiseAlert } from '../../utils/alert-slack'
import { isNonEmpty, normalizeError } from '../../utils/common'
import {
  computeProdAndToursCascade,
  toTypesenseBuffers,
} from '../../services/update-productions-and-tours'
import { setMetricLogger, metric, flushMetrics } from '../../utils/metric'
import { auditProd, auditTour } from '../../utils/audit'
import { Layer, Entity, Action, Result } from '../../utils/metric-vocabulary'
import { DataDispatchService } from '../../services/data-dispatch.service'
import { ddbCall } from '../../utils/ddb-metric-wrapper'

import type {
  SQSHandler,
  SQSBatchItemFailure,
  SQSBatchResponse,
} from 'aws-lambda'
import type { TourItem } from '../../models'

/*──────────────────────── constants ────────────────────────*/
const HANDLER = 'inventory/aggregate'
const MAX_RECEIVES = Number(process.env.SQS_MAX_RECEIVE_COUNT ?? 5)
const YIELD_MS = 3000

/*──────────────────────── helper: build docs ───────────────*/
async function buildDocuments(
  prods: ProductionItem[],
  perfs: PerformanceItem[],
  logger: ReturnType<typeof loggerForSQS>
) {
  const showUpMap = new Map<string, ProductionItem>()
  const showDel = new Map<string, ProductionItem>()
  const tourUpMap = new Map<string, TourItem>()
  const tourDel = new Map<string, TourItem>()

  const mutatedProds: ProductionItem[] = []
  const mutatedTours = new Map<string, TourItem>()

  await Promise.all(
    prods.map(async (p) => {
      const { production, tours, prodChanges, toursChanges } =
        await computeProdAndToursCascade(p, perfs)

      auditProd(logger, production, prodChanges)
      mutatedProds.push(production)

      tours.forEach((t) => mutatedTours.set(t.id, t))
      toursChanges.forEach((tc, i) => auditTour(logger, tours[i]!, tc))

      const { showUpserts, showDeletes, tourUpserts, tourDeletes } =
        toTypesenseBuffers(production, tours)

      showUpserts.forEach((item) => showUpMap.set(item.id, item))
      showDeletes.forEach((item) => showDel.set(item.id, item))
      tourUpserts.forEach((item) => tourUpMap.set(item.id, item))
      tourDeletes.forEach((item) => tourDel.set(item.id, item))
    })
  )

  await Promise.all([
    ...mutatedProds.map((p) => p.put()),
    ...[...mutatedTours.values()].map((t) => t.put()),
  ])

  return {
    showUpserts: [...showUpMap.values()],
    showDeletes: [...showDel.values()],
    tourUpserts: [...tourUpMap.values()],
    tourDeletes: [...tourDel.values()],
  }
}

/*──────────────────────── handler ───────────────────────────*/
interface BatchPayload {
  venueSlug: string
  showSlug: string
  tenantCode: string
}
function isValidPayload(p: Partial<BatchPayload>): p is BatchPayload {
  return Boolean(p?.venueSlug && p.showSlug && p.tenantCode)
}

export const handler: SQSHandler = async (event, ctx) => {
  ctx.callbackWaitsForEmptyEventLoop = false

  const batchStart = Date.now()
  const logger = loggerForSQS(event, ctx, HANDLER)
  setMetricLogger(logger)
  const failures: SQSBatchItemFailure[] = []
  let skipped = 0
  let succeeded = 0

  // init once per batch (typesense + outbox clients)
  const dispatch = await DataDispatchService.init(logger)

  try {
    for (let i = 0; i < event.Records.length; i++) {
      const remaining = ctx.getRemainingTimeInMillis()
      if (remaining < YIELD_MS) {
        const yielded = event.Records.length - i

        metric('lambda_yielded_count', yielded, {
          layer: Layer.LAMBDA,
          action: Action.SUMMARY,
        })

        for (let j = i; j < event.Records.length; j++) {
          failures.push({ itemIdentifier: event.Records[j]!.messageId })
        }
        logger.warn(
          { remainingMs: remaining, yielded },
          'Time budget low: yielding'
        )
        break
      }

      const rec = event.Records[i]!
      const tRec = Date.now()
      metric('sqs_consume_count', 1, {
        layer: Layer.QUEUE,
        entity: 'queue:perf-agg',
      })

      try {
        const receives = Number(rec.attributes?.ApproximateReceiveCount ?? 1)
        const retries = receives - 1
        if (retries > 0) {
          metric('sqs_retry_count', retries, {
            layer: Layer.QUEUE,
            action: Action.RETRY,
          })
        }

        // queue lag per message
        metric(
          'event_lag_ms',
          Date.now() - Number(rec.attributes.SentTimestamp),
          {
            layer: Layer.INGEST,
            action: Action.LAG,
            entity: Entity.PERFORMANCE,
          }
        )

        // parse & validate
        let body: BatchPayload
        try {
          const parsed = JSON.parse(rec.body) as Partial<BatchPayload>
          if (!isValidPayload(parsed)) throw new Error('invalid-payload-shape')

          body = parsed
        } catch (err) {
          failures.push({ itemIdentifier: rec.messageId })
          if (receives >= MAX_RECEIVES) {
            await raiseAlert(
              HANDLER,
              err,
              { phase: 'parse', raw: rec.body },
              ctx
            )
          }
          continue
        }

        const log = logger.child({
          tenantCode: body.tenantCode,
          venueSlug: body.venueSlug,
          showSlug: body.showSlug,
        })
        log.info(
          { messageId: rec.messageId, retries, remainingMs: remaining },
          'Processing message'
        )

        const slugKey = `${body.showSlug}#${body.venueSlug}`

        // DDB reads (instrumented via ddbCall)
        const [prodRes, perfRes] = await Promise.all([
          ddbCall(
            'query',
            ddbClient.send(
              new QueryCommand(buildGSIQuery(`PRODUCTION#${slugKey}`))
            ),
            {
              entity: Entity.SHOW,
              table: process.env.TABLE_NAME,
              access: 'GSI1',
            }
          ),
          ddbCall(
            'query',
            ddbClient.send(
              new QueryCommand(
                buildGSIQuery(`PERFORMANCE#${body.tenantCode}#${slugKey}`)
              )
            ),
            {
              entity: Entity.PERFORMANCE,
              table: process.env.TABLE_NAME,
              access: 'GSI1',
            }
          ),
        ])

        const prods = prodRes.Items?.map(ProductionItem.fromDbItem)
        const perfs = perfRes.Items?.map(PerformanceItem.fromDbItem)

        if (!isNonEmpty(prods) || !isNonEmpty(perfs)) {
          skipped++
          continue
        }

        // cascade + build TS buffers
        const { showUpserts, showDeletes, tourUpserts, tourDeletes } =
          await buildDocuments(prods, perfs, log)

        // helpful counters for dashboards
        const tsTotal =
          showUpserts.length +
          showDeletes.length +
          tourUpserts.length +
          tourDeletes.length
        if (tsTotal) {
          metric('agg_ts_buffer_count', tsTotal, {
            layer: Layer.LAMBDA,
            entity: Entity.SHOW,
            action: Action.SUMMARY,
            result: Result.SUCCESS,
          })
        }

        // bulk Typesense sync (DataDispatchService emits ts_op_count etc.)
        await dispatch.emit([
          { alias: 'show', imports: showUpserts, deletes: showDeletes },
          { alias: 'tour', imports: tourUpserts, deletes: tourDeletes },
        ])

        succeeded++
        metric('sqs_ack_count', 1, {
          layer: Layer.QUEUE,
          entity: 'queue:perf-agg',
          result: Result.SUCCESS,
        })
      } catch (err) {
        metric('sqs_invalid_count', 1, {
          layer: Layer.QUEUE,
          entity: 'queue:perf-agg',
          result: Result.FAILED,
        })
        failures.push({ itemIdentifier: rec.messageId })
        metric('aggregate_error_count', 1, {
          layer: Layer.LAMBDA,
          result: Result.ERROR,
        })
        logger.error(
          {
            ...normalizeError(err),
            remainingMs: ctx.getRemainingTimeInMillis(),
          },
          'Aggregation error'
        )
      } finally {
        // per-record latency (covers both success & failure)
        metric('lambda_record_latency_ms', Date.now() - tRec, {
          layer: Layer.LAMBDA,
          entity: Entity.SHOW,
          action: Action.SUMMARY,
        })
      }
    }
  } finally {
    logger.info(
      {
        total: event.Records.length,
        succeeded,
        skipped,
        failed: failures.length,
        batchMs: Date.now() - batchStart,
      },
      'Batch complete'
    )
    flushMetrics()
  }

  return { batchItemFailures: failures } as SQSBatchResponse
}
