/*───────────────────────────────────────────────────────────────────────────*\
  event/emit-outbox.ts
  – Reads outbox rows (from DynamoDB stream or poll), publishes to EventBus,
    marks emission status, and emits actionable New Relic custom metrics.
\*───────────────────────────────────────────────────────────────────────────*/
import zlib from 'zlib'
import { promisify } from 'util'

import { loggerForLambda, loggerForStream } from '../../utils/logger'
import { EventBus } from '../../clients/eventbus'
import { MAX_EVENTS_PER_BATCH } from '../../utils/constants'
import { normalizeError } from '../../utils/common'
import { alertSlack } from '../../utils/alert-slack'
import {
  FALLBACK_TIMESTAMP,
  Outbox,
  type OutboxRecordPayload,
  type OutboxDbRecord,
} from '../../clients/outbox'
import { metric, setMetricLogger, flushMetrics } from '../../utils/metric'
import {
  Action,
  Entity,
  Layer,
  Op,
  Result,
} from '../../utils/metric-vocabulary'

import type { AttributeValue } from '@aws-sdk/client-dynamodb'
import type { Context, DynamoDBStreamEvent, ScheduledEvent } from 'aws-lambda'
import type { Logger } from '@atg-digital/server-logger-library'

/*──────────────────────── constants ────────────────────────*/
const HANDLER = 'event/emit-outbox'
const STREAM_EVENT_SOURCE = 'aws:dynamodb'

/*──────────────────────── type guards ───────────────────────*/
function isStreamEvent(
  event: DynamoDBStreamEvent | ScheduledEvent
): event is DynamoDBStreamEvent {
  return (
    'Records' in event &&
    Boolean(event.Records.length) &&
    event.Records[0]?.eventSource === STREAM_EVENT_SOURCE
  )
}

export async function parseOutboxPayload(
  payload: Buffer | Uint8Array | object
): Promise<OutboxRecordPayload> {
  const gunzipAsync = promisify(zlib.gunzip)

  const isBufferInstance =
    Buffer.isBuffer(payload) || payload instanceof Uint8Array

  if (!isBufferInstance) return payload as OutboxRecordPayload

  const decompressed = await gunzipAsync(Buffer.from(payload))

  return JSON.parse(decompressed.toString('utf-8')) as OutboxRecordPayload
}

/*──────────────────────── helpers ───────────────────────────*/
async function raiseEvents(events: OutboxDbRecord[], log: Logger) {
  const failed: string[] = []
  const eventBus = new EventBus(log, { entity: Entity.OUTBOX })

  for (const [index, ev] of events.entries()) {
    // Staleness at pickup (actionable for lag alerts)
    const ageMs = Date.now() - Date.parse(ev.createdAt)
    metric('outbox_event_age_ms', ageMs, {
      layer: Layer.EVENTS,
      entity: Entity.OUTBOX,
      action: Action.LAG,
    })

    const payload = await parseOutboxPayload(ev.payload)

    eventBus.addToEntries(payload)

    const isLast = events.length - index === 1

    const shouldPublish =
      eventBus.entriesLength === MAX_EVENTS_PER_BATCH || isLast

    if (!shouldPublish) continue

    const { errors } = await eventBus.publishEvents()

    if (!errors) continue

    errors.forEach((error) => {
      if (!error.entry?.Detail) {
        throw new Error('Event raise with no payload detail')
      }

      const payload = JSON.parse(error.entry.Detail) as {
        metadata: { id: string }
      }

      failed.push(payload.metadata.id)

      log.error(
        { payload, error },
        'Event failed to be published to event bus.'
      )
    })
  }

  log.info(`Raised all ${events.length} events`)
  return { failed }
}

function getEventsFromStream(event: DynamoDBStreamEvent, outbox: Outbox) {
  return event.Records.map((record) => {
    const newImg = record.dynamodb?.NewImage
    if (!newImg) return null
    return outbox.transformRaw(newImg as Record<string, AttributeValue>)
  }).filter(Boolean) as OutboxDbRecord[]
}

/*──────────────────────── handler ───────────────────────────*/
export const handler = async (
  event: DynamoDBStreamEvent | ScheduledEvent,
  ctx: Context
) => {
  const isStream = isStreamEvent(event)

  const baseLogger = isStream
    ? loggerForStream(event, ctx, HANDLER)
    : loggerForLambda(HANDLER, ctx)

  const log = baseLogger.child({
    phase: isStream ? 'outbox-stream' : 'outbox-poll',
  })
  setMetricLogger(log)
  log.info('Outbox emission batch start')

  const outbox = new Outbox()

  try {
    const tFetch = Date.now()
    const events = isStream
      ? getEventsFromStream(event, outbox)
      : await outbox.fetchUniqUnprocessed()

    if (!isStream) {
      metric('outbox_backlog_count', events.length, {
        layer: Layer.DB,
        entity: Entity.OUTBOX,
        action: Action.SUMMARY,
      })
    }

    if (!events.length) {
      log.info('Found 0 events to process')
      return
    }

    const oldestCreatedAt = events.reduce((acc, rec) => {
      const approx = Date.parse(rec.createdAt)
      return approx ? Math.min(acc, approx) : acc
    }, Infinity)

    if (oldestCreatedAt) {
      metric('event_lag_ms', Date.now() - Math.floor(oldestCreatedAt * 1000), {
        layer: Layer.LAMBDA,
        entity: Entity.PERFORMANCE,
        action: Action.LAG,
      })
    }

    metric('outbox_fetch_latency_ms', Date.now() - tFetch, {
      layer: Layer.DB,
      op: Op.QUERY,
      entity: Entity.OUTBOX,
      result: Result.SUCCESS,
    })

    metric('outbox_emit_count', events.length, {
      layer: Layer.EVENTS,
      op: Op.PUT,
      result: Result.SUCCESS,
    })

    log.info(`Raising ${events.length} event items`)
    const { failed } = await raiseEvents(events, log)

    const successEvents = events.filter((e) => !failed.includes(e.sk))

    const eventsAtMaxFails = events.filter(
      (e) => e.attempts === 2 && failed.includes(e.sk)
    )

    log.info(
      { failed: failed.length, success: successEvents.length },
      'Outbox finished publishing'
    )

    const tMark = Date.now()

    await Promise.all([
      ...successEvents.map((e) =>
        outbox.markEmission(e, new Date().toISOString())
      ),
      ...eventsAtMaxFails.map((e) =>
        outbox.markEmission(e, FALLBACK_TIMESTAMP)
      ),
    ])

    metric('outbox_mark_latency_ms', Date.now() - tMark, {
      layer: Layer.DB,
      entity: Entity.OUTBOX,
      op: Op.UPDATE,
      result: Result.SUCCESS,
    })

    metric(
      'outbox_mark_count',
      successEvents.length + eventsAtMaxFails.length,
      {
        layer: Layer.DB,
        entity: Entity.OUTBOX,
        op: Op.UPDATE,
        result: Result.SUCCESS,
      }
    )

    // Aggregate alert on max-attempt give-ups (actionable)
    if (eventsAtMaxFails.length) {
      metric('outbox_max_attempts_count', eventsAtMaxFails.length, {
        layer: Layer.EVENTS,
        entity: Entity.OUTBOX,
        op: Op.PUT,
        result: Result.FAILED,
      })

      await alertSlack({
        handler: HANDLER,
        error: new Error('Outbox publish failures at max attempts'),
        requestId: ctx.awsRequestId,
        detail: {
          count: eventsAtMaxFails.length,
          examples: eventsAtMaxFails
            .slice(0, 5)
            .map((e) => ({ pk: e.pk, sk: e.sk, attempts: e.attempts })),
        },
      })
    }

    log.info('Outbox emission batch processing finished')
  } catch (err) {
    const { error, message, stackLines } = normalizeError(err)
    log.error({ err: error, message, stackLines }, 'Unhandled outbox error')
    await alertSlack({
      handler: HANDLER,
      error,
      requestId: ctx.awsRequestId,
    })
    throw err
  } finally {
    flushMetrics()
  }
}
