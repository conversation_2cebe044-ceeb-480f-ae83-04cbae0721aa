import zlib from 'zlib'
import { promisify } from 'util'

import { marshall } from '@aws-sdk/util-dynamodb'

import {
  Outbox,
  FALLBACK_TIMESTAMP,
  type OutboxDbRecord,
  type OutboxRecordPayload,
} from '../../clients/outbox'
import { EventBus } from '../../clients/eventbus'
import * as loggerUtils from '../../utils/logger'
import * as alertSlackModule from '../../utils/alert-slack'
import * as commonUtils from '../../utils/common'

import { handler } from './emit-outbox'

import type { AttributeValue, Context } from 'aws-lambda'
import type { PutItemCommandOutput } from '@aws-sdk/client-dynamodb'

jest.mock('../../utils/logger')
jest.mock('../../utils/alert-slack')
jest.mock('../../utils/common')

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  child: jest.fn().mockReturnThis(),
  warn: jest.fn(),
}

const publishEventResponse = {} as PutItemCommandOutput

describe('emit-outbox handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(loggerUtils.loggerForStream as jest.Mock).mockReturnValue(mockLogger)
    ;(loggerUtils.loggerForLambda as jest.Mock).mockReturnValue(mockLogger)
  })

  function createStreamEvent(records: Record<string, AttributeValue>[] = []) {
    return {
      Records: records.map((record) => ({
        eventSource: 'aws:dynamodb',
        dynamodb: {
          NewImage: record,
        },
      })),
    }
  }

  function createDdbRecord(
    outboxRecord: Partial<OutboxDbRecord>
  ): Record<string, AttributeValue> {
    return marshall(outboxRecord) as Record<string, AttributeValue>
  }

  function createScheduledEvent() {
    return {
      'source': 'aws.events',
      'id': 'test-id',
      'version': '0',
      'account': 'test-account',
      'time': '2023-01-01T00:00:00Z',
      'region': 'us-east-1',
      'resources': [],
      'detail-type': 'Scheduled Event' as const,
      'detail': {},
    }
  }

  function createContext(requestId = 'test-request-id') {
    return { awsRequestId: requestId } as Context
  }

  async function zipPayload(payload: OutboxRecordPayload): Promise<Buffer> {
    const gzipAsync = promisify(zlib.gzip)

    return gzipAsync(Buffer.from(JSON.stringify(payload), 'utf-8'))
  }

  async function unzipBuffer(payload: Buffer): Promise<OutboxRecordPayload> {
    const gunzipAsync = promisify(zlib.gunzip)

    const buffer = await gunzipAsync(payload)

    return JSON.parse(buffer.toString('utf-8')) as OutboxRecordPayload
  }

  describe('Stream Event Processing', () => {
    it('should process stream events where buffer payload exists with all events succeeding', async () => {
      const outboxRecord: OutboxDbRecord = {
        pk: 'test-pk',
        sk: 'test-sk',
        createdAt: '2023-01-01T00:00:00Z',
        attempts: 0,
        payload: await zipPayload({
          EventBusName: 'test-bus',
          DetailType: 'publish',
          Detail: '{"test": "data"}',
          Source: 'atg.catalogue-aggregator.production',
        }),
      }

      const streamEvent = createStreamEvent([createDdbRecord(outboxRecord)])
      const context = createContext('stream-success-test')

      jest
        .spyOn(Outbox.prototype, 'transformRaw')
        .mockImplementation(() => outboxRecord)

      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: null,
        publishEventResponse,
      })

      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 1,
      })

      await handler(streamEvent, context)

      expect(loggerUtils.loggerForStream).toHaveBeenCalledWith(
        streamEvent,
        context,
        'event/emit-outbox'
      )
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Outbox emission batch start'
      )
      expect(mockLogger.info).toHaveBeenCalledWith('Raising 1 event items')
      expect(mockLogger.info).toHaveBeenCalledWith('Raised all 1 events')
      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 0, success: 1 },
        'Outbox finished publishing'
      )
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Outbox emission batch processing finished'
      )
      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecord,
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )
      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })

    it('should process stream events where legacy json payload exists with all events succeeding', async () => {
      const outboxRecord: OutboxDbRecord = {
        pk: 'test-pk',
        sk: 'test-sk',
        createdAt: '2023-01-01T00:00:00Z',
        attempts: 0,
        payload: {
          EventBusName: 'test-bus',
          DetailType: 'publish',
          Detail: '{"test": "data"}',
          Source: 'atg.catalogue-aggregator.production',
        },
      }

      const streamEvent = createStreamEvent([createDdbRecord(outboxRecord)])
      const context = createContext('stream-success-test')

      jest
        .spyOn(Outbox.prototype, 'transformRaw')
        .mockImplementation(() => outboxRecord)

      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: null,
        publishEventResponse,
      })

      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 1,
      })

      await handler(streamEvent, context)

      expect(loggerUtils.loggerForStream).toHaveBeenCalledWith(
        streamEvent,
        context,
        'event/emit-outbox'
      )
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Outbox emission batch start'
      )
      expect(mockLogger.info).toHaveBeenCalledWith('Raising 1 event items')
      expect(mockLogger.info).toHaveBeenCalledWith('Raised all 1 events')
      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 0, success: 1 },
        'Outbox finished publishing'
      )
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Outbox emission batch processing finished'
      )
      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecord,
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )
      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })

    it('should process stream events with some events failing at max attempts', async () => {
      const successfulRecord: OutboxDbRecord = {
        pk: 'success-pk',
        sk: 'success-sk',
        createdAt: '2023-01-01T00:00:00Z',
        attempts: 0,
        payload: await zipPayload({
          EventBusName: 'test-bus',
          DetailType: 'publish',
          Detail: '{"test": "success"}',
          Source: 'atg.catalogue-aggregator.production',
        }),
      }

      const failedRecord: OutboxDbRecord = {
        pk: 'failed-pk',
        sk: 'failed-sk',
        createdAt: '2023-01-01T00:00:00Z',
        attempts: 2,
        payload: await zipPayload({
          EventBusName: 'test-bus',
          DetailType: 'publish',
          Detail: '{"test": "failed", "metadata": { "id": "failed-sk" } }',
          Source: 'atg.catalogue-aggregator.production',
        }),
      }

      const streamEvent = createStreamEvent([
        createDdbRecord(successfulRecord),
        createDdbRecord(failedRecord),
      ])
      const context = createContext('stream-failure-test')

      jest
        .spyOn(Outbox.prototype, 'transformRaw')
        .mockReturnValueOnce(successfulRecord)
        .mockReturnValueOnce(failedRecord)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: [
          {
            error: { code: 'FAILED', message: 'Failed to publish' },
            entry: await unzipBuffer(failedRecord.payload as Buffer),
          },
        ],
        publishEventResponse,
      })
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 2,
      })

      await handler(streamEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith('Raising 2 event items')
      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 1, success: 1 },
        'Outbox finished publishing'
      )

      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        successfulRecord,
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )

      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        failedRecord,
        FALLBACK_TIMESTAMP
      )

      expect(alertSlackModule.alertSlack).toHaveBeenCalledWith(
        expect.objectContaining({
          handler: 'event/emit-outbox',
          requestId: context.awsRequestId,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          error: expect.objectContaining({
            message: 'Outbox publish failures at max attempts',
          }),
          detail: {
            count: 1,
            examples: [
              {
                pk: failedRecord.pk,
                sk: failedRecord.sk,
                attempts: failedRecord.attempts,
              },
            ],
          },
        })
      )
    })

    it('should handle stream events with no NewImage', async () => {
      const streamEvent = {
        Records: [
          {
            eventSource: 'aws:dynamodb',
            dynamodb: {
              NewImage: undefined,
            },
          },
        ],
      }
      const context = createContext('stream-no-image-test')

      await handler(streamEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith('Found 0 events to process')
      expect(Outbox.prototype.markEmission).not.toHaveBeenCalled()
      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })
  })

  describe('Scheduled Event Processing (Polling)', () => {
    it('should process scheduled events with all events succeeding', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('scheduled-success-test')

      const outboxRecords: OutboxDbRecord[] = [
        {
          pk: 'pk1',
          sk: 'sk1',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 0,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: '{"test": "data1"}',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
        {
          pk: 'pk2',
          sk: 'sk2',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 0,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: '{"test": "data2"}',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
      ]

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockResolvedValue(outboxRecords)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: null,
        publishEventResponse,
      })
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 2,
      })

      await handler(scheduledEvent, context)

      expect(loggerUtils.loggerForLambda).toHaveBeenCalledWith(
        'event/emit-outbox',
        context
      )
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Outbox emission batch start'
      )
      expect(mockLogger.info).toHaveBeenCalledWith('Raising 2 event items')
      expect(mockLogger.info).toHaveBeenCalledWith('Raised all 2 events')
      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 0, success: 2 },
        'Outbox finished publishing'
      )
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Outbox emission batch processing finished'
      )

      expect(Outbox.prototype.markEmission).toHaveBeenCalledTimes(2)
      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecords[0],
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )
      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecords[1],
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )

      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })

    it('should process scheduled events with no events to process', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('scheduled-empty-test')

      jest.spyOn(Outbox.prototype, 'fetchUniqUnprocessed').mockResolvedValue([])

      await handler(scheduledEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith('Found 0 events to process')
      expect(Outbox.prototype.markEmission).not.toHaveBeenCalled()
      // eslint-disable-next-line  @typescript-eslint/unbound-method
      expect(EventBus.prototype.addToEntries).not.toHaveBeenCalled()
      // eslint-disable-next-line  @typescript-eslint/unbound-method
      expect(EventBus.prototype.publishEvents).not.toHaveBeenCalled()
      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })

    it('should handle scheduled events with partial failures not at max attempts', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('scheduled-partial-failure-test')

      const outboxRecords: OutboxDbRecord[] = [
        {
          pk: 'success-pk',
          sk: 'success-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 1,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: '{"test": "success"}',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
        {
          pk: 'failed-pk',
          sk: 'failed-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 1,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: '{"test": "failed", "metadata": { "id": "failed-sk" } }',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
      ]

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockResolvedValue(outboxRecords)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: [
          {
            error: { code: 'FAILED', message: 'Failed to publish' },
            entry: await unzipBuffer(outboxRecords[1]!.payload as Buffer),
          },
        ],
        publishEventResponse,
      })
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 2,
      })

      await handler(scheduledEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 1, success: 1 },
        'Outbox finished publishing'
      )

      expect(Outbox.prototype.markEmission).toHaveBeenCalledTimes(1)
      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecords[0],
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )

      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })

    it('should handle scheduled events with failures at max attempts', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('scheduled-max-failure-test')

      const outboxRecords: OutboxDbRecord[] = [
        {
          pk: 'max-failed-pk',
          sk: 'max-failed-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 2,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail:
              '{"test": "max-failed", "metadata": { "id": "max-failed-sk" } }',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
      ]

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockResolvedValue(outboxRecords)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: [
          {
            error: { code: 'FAILED', message: 'Failed to publish' },
            entry: await unzipBuffer(outboxRecords[0]!.payload as Buffer),
          },
        ],
        publishEventResponse,
      })
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 1,
      })

      await handler(scheduledEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 1, success: 0 },
        'Outbox finished publishing'
      )

      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecords[0],
        FALLBACK_TIMESTAMP
      )
      expect(alertSlackModule.alertSlack).toHaveBeenCalledWith(
        expect.objectContaining({
          handler: 'event/emit-outbox',
          requestId: context.awsRequestId,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          error: expect.objectContaining({
            message: 'Outbox publish failures at max attempts',
          }),
          detail: {
            count: 1,
            examples: [
              {
                pk: outboxRecords[0]!.pk,
                sk: outboxRecords[0]!.sk,
                attempts: outboxRecords[0]!.attempts,
              },
            ],
          },
        })
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle and log errors during stream processing', async () => {
      const streamEvent = createStreamEvent([
        createDdbRecord({ pk: 'test-pk' }),
      ])
      const context = createContext('stream-error-test')
      const error = new Error('Stream processing error')

      jest.spyOn(Outbox.prototype, 'transformRaw').mockImplementation(() => {
        throw error
      })
      ;(commonUtils.normalizeError as jest.Mock).mockReturnValue({
        error,
        message: 'Stream processing error',
        stackLines: ['Error: Stream processing error'],
      })

      await expect(handler(streamEvent, context)).rejects.toThrow(
        'Stream processing error'
      )

      expect(mockLogger.error).toHaveBeenCalledWith(
        {
          err: error,
          message: 'Stream processing error',
          stackLines: ['Error: Stream processing error'],
        },
        'Unhandled outbox error'
      )

      expect(alertSlackModule.alertSlack).toHaveBeenCalledWith({
        handler: 'event/emit-outbox',
        error,
        requestId: context.awsRequestId,
      })
    })

    it('should handle and log errors during scheduled processing', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('scheduled-error-test')
      const error = new Error('Scheduled processing error')

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockRejectedValue(error)
      ;(commonUtils.normalizeError as jest.Mock).mockReturnValue({
        error,
        message: 'Scheduled processing error',
        stackLines: ['Error: Scheduled processing error'],
      })

      await expect(handler(scheduledEvent, context)).rejects.toThrow(
        'Scheduled processing error'
      )

      expect(mockLogger.error).toHaveBeenCalledWith(
        {
          err: error,
          message: 'Scheduled processing error',
          stackLines: ['Error: Scheduled processing error'],
        },
        'Unhandled outbox error'
      )

      expect(alertSlackModule.alertSlack).toHaveBeenCalledWith({
        handler: 'event/emit-outbox',
        error,
        requestId: context.awsRequestId,
      })
    })

    it('should handle EventBus publish errors', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('eventbus-error-test')

      const outboxRecords: OutboxDbRecord[] = [
        {
          pk: 'test-pk',
          sk: 'test-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 0,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: '{"test": "data"}',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
      ]

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockResolvedValue(outboxRecords)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest
        .spyOn(EventBus.prototype, 'publishEvents')
        .mockRejectedValue(new Error('EventBus publish error'))
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 1,
      })
      ;(commonUtils.normalizeError as jest.Mock).mockReturnValue({
        error: new Error('EventBus publish error'),
        message: 'EventBus publish error',
        stackLines: ['Error: EventBus publish error'],
      })

      await expect(handler(scheduledEvent, context)).rejects.toThrow(
        'EventBus publish error'
      )

      expect(alertSlackModule.alertSlack).toHaveBeenCalledWith({
        handler: 'event/emit-outbox',
        error: new Error('EventBus publish error'),
        requestId: context.awsRequestId,
      })
    })
  })

  describe('Edge Cases', () => {
    it('should handle mixed success and failure scenarios', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('mixed-scenario-test')

      const outboxRecords: OutboxDbRecord[] = [
        {
          pk: 'success-pk',
          sk: 'success-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 0,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: '{"test": "success"}',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
        {
          pk: 'failed-not-max-pk',
          sk: 'failed-not-max-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 1,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail:
              '{"test": "failed-not-max", "metadata": { "id": "failed-not-max-sk"} }',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
        {
          pk: 'failed-max-pk',
          sk: 'failed-max-sk',
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 2,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail:
              '{"test": "failed-max", "metadata": { "id": "failed-max-sk" } }',
            Source: 'atg.catalogue-aggregator.production',
          }),
        },
      ]

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockResolvedValue(outboxRecords)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: [
          {
            error: { code: 'FAILED', message: 'Failed to publish' },
            entry: await unzipBuffer(outboxRecords[1]!.payload as Buffer),
          },
          {
            error: { code: 'FAILED', message: 'Failed to publish' },
            entry: await unzipBuffer(outboxRecords[2]!.payload as Buffer),
          },
        ],
        publishEventResponse,
      })
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 3,
      })

      await handler(scheduledEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith(
        { failed: 2, success: 1 },
        'Outbox finished publishing'
      )

      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecords[0],
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
      )
      expect(Outbox.prototype.markEmission).toHaveBeenCalledWith(
        outboxRecords[2],
        FALLBACK_TIMESTAMP
      )

      expect(alertSlackModule.alertSlack).toHaveBeenCalledTimes(1)
      expect(alertSlackModule.alertSlack).toHaveBeenCalledWith(
        expect.objectContaining({
          handler: 'event/emit-outbox',
          requestId: context.awsRequestId,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          error: expect.objectContaining({
            message: 'Outbox publish failures at max attempts',
          }),
          detail: {
            count: 1,
            examples: [
              {
                pk: outboxRecords[2]!.pk,
                sk: outboxRecords[2]!.sk,
                attempts: outboxRecords[2]!.attempts,
              },
            ],
          },
        })
      )
    })

    it('should handle large batches of events', async () => {
      const scheduledEvent = createScheduledEvent()
      const context = createContext('large-batch-test')

      const outboxRecords: Promise<OutboxDbRecord>[] = Array.from(
        { length: 50 },
        async (_, i) => ({
          pk: `pk-${i}`,
          sk: `sk-${i}`,
          createdAt: '2023-01-01T00:00:00Z',
          attempts: 0,
          payload: await zipPayload({
            EventBusName: 'test-bus',
            DetailType: 'publish',
            Detail: `{"test": "data-${i}"}`,
            Source: 'atg.catalogue-aggregator.production',
          }),
        })
      )

      const resolvedOutboxRecords = await Promise.all(outboxRecords)

      jest
        .spyOn(Outbox.prototype, 'fetchUniqUnprocessed')
        .mockResolvedValue(resolvedOutboxRecords)
      jest.spyOn(Outbox.prototype, 'markEmission').mockResolvedValue(undefined)

      jest.spyOn(EventBus.prototype, 'addToEntries')

      jest.spyOn(EventBus.prototype, 'publishEvents').mockResolvedValue({
        errors: null,
        publishEventResponse,
      })
      Object.defineProperty(EventBus.prototype, 'entriesLength', {
        get: () => 50,
      })

      await handler(scheduledEvent, context)

      expect(mockLogger.info).toHaveBeenCalledWith('Raising 50 event items')
      expect(mockLogger.info).toHaveBeenCalledWith('Raised all 50 events')
      expect(Outbox.prototype.markEmission).toHaveBeenCalledTimes(50)
      expect(alertSlackModule.alertSlack).not.toHaveBeenCalled()
    })
  })
})
