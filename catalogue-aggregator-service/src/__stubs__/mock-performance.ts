import { Av } from '../models/inventory/av'
import { PerformanceItem } from '../models/inventory/performance'

import type { Payload } from '../models/inventory/performance'
import type { Status } from '../interfaces'

/**
 * Generates a stub PerformanceItem.
 * You can override any of the default payload values by passing an object.
 */
export const generatePerformanceStub = (
  overrides?: Partial<Payload & { status: Status }>
): PerformanceItem => {
  const defaultPayload: Payload = {
    sourceType: 'stub-instance',
    sourceId: 'stub-id',
    titleSlug: 'stub-title',
    venueSlug: 'stub-venue',
    minPrice: 50,
    minFee: 5,
    maxFee: 50,
    maxPrice: 100,
    transactionFee: 2,
    onSaleStartDate: '2022-01-01T10:00:00+00:00',
    preSaleStarts: '2021-12-31T10:00:00+00:00',
    startDate: '2022-01-02T10:00:00+00:00',
    embargoEnds: undefined,
    offSaleDate: undefined,
    timezone: 'UTC',
  }

  const payload: Payload = { ...defaultPayload, ...overrides }

  const inventorySystem = new Av()
  const perf = new PerformanceItem(inventorySystem, payload)

  perf.tenantCode = 'atg'
  perf.isOnCalendar = true

  if (overrides?.status) perf.status = overrides.status
  return perf
}
