{"name": "catalogue-aggregator-service", "version": "1.105.2", "private": true, "description": "Data aggregation layer for the Global Catalogue API service", "repository": {"type": "git", "url": "**************:atg-digital/catalogue-aggregator-service"}, "author": "ATG", "main": "src/lambda.ts", "scripts": {"prebuild": "rm -rf dist", "build": "tsc", "deploy:cleanup": "make cleanup", "deploy:production": "STAGE=production make deploy", "deploy:staging": "STAGE=staging make deploy", "deploy:test": "STAGE=test make deploy", "deploy:uat": "STAGE=uat make deploy", "format": "prettier --write .", "format:check": "prettier --check .", "format:tf": "terraform fmt -recursive -check -diff", "format:tf:fix": "terraform fmt -recursive -write", "preinstall": "npx only-allow pnpm", "lint": "eslint . --ext .js,.ts", "lint-format": "pnpm run lint && pnpm run format:check", "lint-format:fix": "pnpm run lint:fix && pnpm run format", "lint:fix": "eslint . --ext .js,.ts --fix", "dev:start": "./scripts/dev-start.sh", "dev:status": "./scripts/dev-status.sh", "dev:reset": "./scripts/dev-reset.sh", "dev:logs": "./scripts/dev-logs.sh", "dev:validate": "./scripts/dev-validate.sh", "dev:test": "./scripts/dev-test.sh", "plan:production": "env TF_WORKSPACE=production pnpm run terraform:plan", "plan:staging": "env TF_WORKSPACE=staging pnpm run terraform:plan", "plan:uat": "env TF_WORKSPACE=uat pnpm run terraform:plan", "prepare": "husky", "release": "semantic-release", "start": "sls offline start --reload<PERSON><PERSON><PERSON>", "terraform:apply": "cd terraform && terraform:init && terraform apply", "terraform:init": "cd terraform && terraform init", "terraform:plan": "cd terraform && terraform:init && cd terraform && terraform plan", "test": "jest --passWithNoTests", "test:e2e": "jest --config jest.config.e2e.js --passWithNoTests", "test:integration": "jest --config jest.config.integration.js --runInBand", "test:playwright": "npx playwright test", "test:watch": "jest --watch", "test:coverage": "jest --silent --coverage", "typecheck": "tsc", "typesense-dev": "chmod +x ./src/typesense/local/init.sh && ./src/typesense/local/init.sh", "schema-migrate": "npx ts-node ./src/typesense/scripts/codegen.ts"}, "dependencies": {"@atg-digital/cms-types": "^1.133.0", "@atg-digital/server-logger-library": "^4.2.3", "@aws-sdk/client-cloudwatch": "^3.862.0", "@aws-sdk/client-dynamodb": "^3.862.0", "@aws-sdk/client-eventbridge": "^3.862.0", "@aws-sdk/client-scheduler": "^3.862.0", "@aws-sdk/client-secrets-manager": "^3.876.0", "@aws-sdk/client-sfn": "^3.862.0", "@aws-sdk/client-sqs": "^3.862.0", "@aws-sdk/client-ssm": "^3.862.0", "@aws-sdk/node-http-handler": "^3.374.0", "@aws-sdk/util-dynamodb": "^3.862.0", "@slack/webhook": "^7.0.4", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "colorette": "^2.0.20", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "typesense": "^2.0.3", "uuid": "^11.1.0", "zlib": "^1.0.5", "zod": "^3.25.67"}, "devDependencies": {"@atg-digital/config": "^3.0.3", "@atg-digital/eslint-config": "^2.0.1", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@playwright/test": "^1.52.0", "@semantic-release/commit-analyzer": "^11.1.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.2.6", "@semantic-release/npm": "^11.0.3", "@semantic-release/release-notes-generator": "^12.1.0", "@serverless/typescript": "^3.38.0", "@slack/types": "^2.14.0", "@swc/core": "^1.9.3", "@swc/jest": "^0.2.37", "@testcontainers/localstack": "10.13.2", "@tsconfig/recommended": "^1.0.8", "@tsconfig/strictest": "^2.0.5", "@types/aws-lambda": "^8.10.146", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/newrelic": "^9.14.8", "@types/node": "^20.17.9", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "aws-sdk-client-mock": "^4.1.0", "aws-sdk-client-mock-jest": "^4.1.0", "commitlint": "^19.6.0", "esbuild": "^0.24.0", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jest": "27.9.0", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-promise": "6.1.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.2.10", "newrelic": "^12.8.0", "prettier": "^3.4.1", "rimraf": "^6.0.1", "semantic-release": "^22.0.12", "semantic-release-slack-bot": "^4.0.2", "serverless": "^3.39.0", "serverless-esbuild": "^1.54.6", "serverless-localstack": "^1.3.1", "serverless-newrelic-lambda-layers": "^5.5.0", "serverless-offline": "^13.8.3", "serverless-plugin-lambda-insights": "^2.0.0", "serverless-step-functions": "^3.21.2", "testcontainers": "10.13.2", "ts-loader": "^9.5.1", "ts-node": "^9.1.1", "typescript": "^5.7.2"}, "engines": {"node": ">=20", "pnpm": ">=9"}, "pnpm": {"overrides": {"@smithy/*": "4.3.2"}}}