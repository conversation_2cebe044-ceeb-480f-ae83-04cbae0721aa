###############################################################################
# IAM  — complete, copy-paste-ready
# ─────────────────────────────────────────────────────────────────────────────
# • SQS-driven Lambdas           →  aws_iam_role.sqs_role
# • EventBridge handler <PERSON><PERSON>  →  aws_iam_role.eventbus_handler_role
# • Stream listener Lambdas      →  aws_iam_role.stream_listener_role
# • One-shot schedule authoring  →  scheduler_write_policy  (attached to sqs_role)
# • Scheduler runtime            →  scheduler_invoke_lambda_role  (+ policy)
###############################################################################

################################################################################
# 0.  Shared policies
################################################################################

# ── DynamoDB R/W on the catalogue table
resource "aws_iam_policy" "dynamodb_access_policy" {
  name = "${local.resource_prefix}-dynamodb-access-policy-${local.environment}"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Action = [
        "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:BatchWriteItem",
        "dynamodb:DeleteItem", "dynamodb:BatchGetItem", "dynamodb:GetItem",
        "dynamodb:Query", "dynamodb:Scan"
      ]
      Resource = [
        aws_dynamodb_table.catalogue_aggregation_table.arn,
        "${aws_dynamodb_table.catalogue_aggregation_table.arn}/index/*",
        aws_dynamodb_table.event_outbox.arn,
        "${aws_dynamodb_table.event_outbox.arn}/index/*",
        aws_dynamodb_table.bolt_pricing_ingress_raw.arn,
      ]
    }]
  })
}

# ── Secrets Manager read (and KMS decrypt) — reused by several roles
resource "aws_iam_policy" "secrets_manager_read" {
  name        = "${local.resource_prefix}-secrets-manager-policy-${local.environment}"
  description = "Read access to Secrets Manager + decrypt via KMS"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = ["kms:Decrypt"]
        Resource = [
          "arn:aws:kms:eu-west-2:${var.shared_secrets_aws_accounts[local.environment]}:key/*"
        ]
        Condition = {
          "ForAnyValue:StringEquals" : {
            "kms:ResourceAliases" : "alias/secretsmanager"
          }
        }
      },
      {
        Effect   = "Allow"
        Action   = "secretsmanager:GetSecretValue"
        Resource = ["${local.typesense_credentials_key_arn}*"]
      }
    ]
  })
}

################################################################################
# 1.  SQS-driven Lambdas  (upsertPerformance, aggregate, etc.)
################################################################################

resource "aws_iam_role" "sqs_role" {
  name               = "${local.resource_prefix}-sqs-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy
}

resource "aws_iam_role_policy_attachment" "sqs_dynamodb" {
  role       = aws_iam_role.sqs_role.name
  policy_arn = aws_iam_policy.dynamodb_access_policy.arn
}

resource "aws_iam_role_policy_attachment" "sqs_full_access" {
  role       = aws_iam_role.sqs_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSQSFullAccess"
}

resource "aws_iam_role_policy_attachment" "sqs_secrets" {
  role       = aws_iam_role.sqs_role.name
  policy_arn = aws_iam_policy.secrets_manager_read.arn
}

################################################################################
# 1-A  Scheduler authoring rights (CREATE / DELETE schedule)
#      ➜ attach to the *same* sqs_role because that role runs upsertPerformance
################################################################################

# IAM role that Scheduler assumes *at runtime* (see §3) – created first
resource "aws_iam_role" "scheduler_invoke_lambda_role" {
  name = "${local.resource_prefix}-scheduler-role-${local.environment}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect    = "Allow"
      Principal = { Service = "scheduler.amazonaws.com" }
      Action    = "sts:AssumeRole"
    }]
  })
}

# Policy: allow that role to invoke **any** Lambda (narrow later if you want)
resource "aws_iam_policy" "scheduler_invoke_lambda_policy" {
  name = "${local.resource_prefix}-scheduler-invoke-lambda-${local.environment}"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect   = "Allow"
      Action   = ["lambda:InvokeFunction", "lambda:InvokeAsync"]
      Resource = "arn:aws:lambda:eu-west-2:${var.aws_accounts[local.deploy_env]}:function:*"
      },
      {
        Effect   = "Allow"
        Action   = "states:StartExecution"
        Resource = "*"
    }]
  })
}

# Policy: let the Lambda create / delete schedules, and pass the invoke role
resource "aws_iam_policy" "scheduler_write_policy" {
  name = "${local.resource_prefix}-scheduler-write-${local.environment}"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = ["scheduler:CreateSchedule", "scheduler:DeleteSchedule",
        "scheduler:GetSchedule", "scheduler:UpdateSchedule"]
        Resource = [
          "arn:aws:scheduler:eu-west-2:${var.aws_accounts[local.deploy_env]}:schedule/${aws_scheduler_schedule_group.catalogue_schedule_group.name}/*",
          aws_scheduler_schedule_group.catalogue_schedule_group.arn,
        ],
      },
      {
        Effect   = "Allow"
        Action   = "iam:PassRole"
        Resource = aws_iam_role.scheduler_invoke_lambda_role.arn
        Condition = {
          StringEquals = { "iam:PassedToService" : "scheduler.amazonaws.com" }
        }
      }
    ]
  })
}

# ⬅️  Attach that policy to the SQS Lambda execution role (NOT to a policy!)
resource "aws_iam_role_policy_attachment" "sqs_scheduler_write" {
  role       = aws_iam_role.sqs_role.name
  policy_arn = aws_iam_policy.scheduler_write_policy.arn
}

################################################################################
# 2.  EventBridge handler Lambdas
################################################################################

resource "aws_iam_role" "eventbus_handler_role" {
  name               = "${local.resource_prefix}-read-write-access-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy
}

resource "aws_iam_policy" "eventbus_write_access_policy" {
  name        = "${local.resource_prefix}-eventbus-write-access-policy-${local.environment}"
  description = "Put events to EventBridge"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect   = "Allow"
      Action   = "events:PutEvents"
      Resource = "*"
    }]
  })
}

resource "aws_iam_role_policy_attachment" "eventbus_handler_ddb" {
  role       = aws_iam_role.eventbus_handler_role.name
  policy_arn = aws_iam_policy.dynamodb_access_policy.arn
}

resource "aws_iam_role_policy_attachment" "eventbus_handler_eventbridge" {
  role       = aws_iam_role.eventbus_handler_role.name
  policy_arn = aws_iam_policy.eventbus_write_access_policy.arn
}

resource "aws_iam_role_policy_attachment" "eventbus_handler_secrets" {
  role       = aws_iam_role.eventbus_handler_role.name
  policy_arn = aws_iam_policy.secrets_manager_read.arn
}

resource "aws_iam_role_policy_attachment" "eventbus_handler_fifo_sqs" {
  role       = aws_iam_role.eventbus_handler_role.name
  policy_arn = aws_iam_policy.fifo_send_message_policy.arn
}

################################################################################
# 3.  Stream-listener Lambdas
################################################################################

resource "aws_iam_role" "stream_listener_role" {
  name               = "${local.resource_prefix}-stream-listener-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy
}

resource "aws_iam_policy" "stream_listener_and_eventbus_write_access_policy" {
  name = "${local.resource_prefix}-stream_listener-and-eventbus-${local.environment}"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:DescribeStream",
          "dynamodb:ListStreams"
        ]
        Resource = [
          "${aws_dynamodb_table.catalogue_aggregation_table.arn}/stream/*",
          "${aws_dynamodb_table.event_outbox.arn}/stream/*"
        ]
      },
      {
        Effect = "Allow"
        Action = ["dynamodb:GetItem", "dynamodb:Query", "dynamodb:PutItem", "dynamodb:Scan", "dynamodb:BatchWriteItem", "dynamodb:UpdateItem"]
        Resource = [
          aws_dynamodb_table.catalogue_aggregation_table.arn,
          aws_dynamodb_table.event_outbox.arn,
          "${aws_dynamodb_table.event_outbox.arn}/index/*",
          "${aws_dynamodb_table.catalogue_aggregation_table.arn}/index/*"
        ]
      }
    ]
  })
}

resource "aws_iam_policy" "fifo_send_message_policy" {
  name = "${local.resource_prefix}-fifo-send-message-policy-${local.environment}"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect   = "Allow"
      Action   = "sqs:SendMessage"
      Resource = [aws_sqs_queue.performances_to_shows_queue.arn, aws_sqs_queue.schedule_jobs_queue.arn]
    }]
  })
}

resource "aws_iam_role_policy_attachment" "stream_listener_ddb_stream" {
  role       = aws_iam_role.stream_listener_role.name
  policy_arn = aws_iam_policy.stream_listener_and_eventbus_write_access_policy.arn
}

resource "aws_iam_role_policy_attachment" "stream_listener_fifo_sqs" {
  role       = aws_iam_role.stream_listener_role.name
  policy_arn = aws_iam_policy.fifo_send_message_policy.arn
}

resource "aws_iam_role_policy_attachment" "stream_listener_eventbridge" {
  role       = aws_iam_role.stream_listener_role.name
  policy_arn = aws_iam_policy.eventbus_write_access_policy.arn
}

resource "aws_iam_role_policy_attachment" "stream_listener_secrets" {
  role       = aws_iam_role.stream_listener_role.name
  policy_arn = aws_iam_policy.secrets_manager_read.arn
}

resource "aws_iam_role_policy_attachment" "stream_listener_states" {
  role       = aws_iam_role.stream_listener_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSStepFunctionsFullAccess"
}

resource "aws_iam_role_policy_attachment" "stream_listener_scheduler" {
  role       = aws_iam_role.stream_listener_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEventBridgeSchedulerFullAccess"
}

################################################################################
# 4.  Scheduler runtime (role used BY Scheduler to call the re-evaluate Lambda)
################################################################################

resource "aws_iam_role_policy_attachment" "scheduler_invoke_lambda" {
  role       = aws_iam_role.scheduler_invoke_lambda_role.name
  policy_arn = aws_iam_policy.scheduler_invoke_lambda_policy.arn
}

################################################################################
# 5.  Performance re-evaluate Lambda (Dynamo + Secrets)
################################################################################

resource "aws_iam_role" "performance_reevaluate_role" {
  name               = "${local.resource_prefix}-performance-reevaluate-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy
}

resource "aws_iam_role_policy_attachment" "performance_reevaluate_basic" {
  role       = aws_iam_role.performance_reevaluate_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "performance_reevaluate_ddb" {
  role       = aws_iam_role.performance_reevaluate_role.name
  policy_arn = aws_iam_policy.dynamodb_access_policy.arn
}

resource "aws_iam_role_policy_attachment" "performance_reevaluate_secrets" {
  role       = aws_iam_role.performance_reevaluate_role.name
  policy_arn = aws_iam_policy.secrets_manager_read.arn
}

################################################################################
# 6.  Attach Lambda basic-execution to every execution role in local.iam_roles
################################################################################

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  for_each = local.iam_roles # make sure sqs_role, eventbus_handler_role,
  # stream_listener_role, performance_reevaluate_role
  # are all included in this map
  role       = each.value
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

###############################################################################
# IAM – execution role for the BOLT-pricing Ingress Lambda
###############################################################################
resource "aws_iam_role" "bolt_pricing_ingress_role" {
  name               = "${local.resource_prefix}-bolt-pricing-ingress-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy
}

# --- 1. Scoped SQS receive/delete ---
resource "aws_iam_policy" "bolt_pricing_sqs_consume" {
  name = "${local.resource_prefix}-bolt-pricing-sqs-consume-${local.environment}"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Action = [
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes",
        "sqs:ChangeMessageVisibility"
      ],
      Resource = aws_sqs_queue.bolt_pricing_queue.arn
    }]
  })
}
resource "aws_iam_role_policy_attachment" "bp_ingress_sqs" {
  role       = aws_iam_role.bolt_pricing_ingress_role.name
  policy_arn = aws_iam_policy.bolt_pricing_sqs_consume.arn
}

# --- 2. Write access to raw-events table ---
resource "aws_iam_policy" "bolt_pricing_dynamodb_write" {
  name = "${local.resource_prefix}-bolt-pricing-ddb-write-${local.environment}"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Action = [
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:BatchWriteItem"
      ],
      Resource = [
        aws_dynamodb_table.bolt_pricing_ingress_raw.arn,
        "${aws_dynamodb_table.bolt_pricing_ingress_raw.arn}/index/*"
      ]
    }]
  })
}
resource "aws_iam_role_policy_attachment" "bp_ingress_ddb" {
  role       = aws_iam_role.bolt_pricing_ingress_role.name
  policy_arn = aws_iam_policy.bolt_pricing_dynamodb_write.arn
}

# --- 3. Explicit DENY on CloudWatch Logs ---
resource "aws_iam_policy" "deny_cloudwatch_logs" {
  name = "${local.resource_prefix}-deny-cw-logs-${local.environment}"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Deny",
      Action = [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      Resource = "*"
    }]
  })
}
resource "aws_iam_role_policy_attachment" "bp_ingress_deny_logs" {
  role       = aws_iam_role.bolt_pricing_ingress_role.name
  policy_arn = aws_iam_policy.deny_cloudwatch_logs.arn
}


###############################################################################
# IAM – Bolt-pricing stream-to-catalogue writer
###############################################################################

resource "aws_iam_role" "bolt_pricing_stream_role" {
  name               = "${local.resource_prefix}-bolt-pricing-stream-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy # same trust policy template
  tags               = local.resource_tags
}

/* 1️⃣  READ the DynamoDB Stream on bolt_pricing_ingress_raw */
resource "aws_iam_policy" "bp_stream_read_policy" {
  name = "${local.resource_prefix}-bp-stream-read-${local.environment}"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Action = [
        "dynamodb:GetRecords",
        "dynamodb:GetShardIterator",
        "dynamodb:DescribeStream",
        "dynamodb:ListStreams"
      ]
      Resource = "${aws_dynamodb_table.bolt_pricing_ingress_raw.arn}/stream/*"
    }]
  })
}

/* 2️⃣  WRITE (and query) the catalogue_aggregation_table */
resource "aws_iam_policy" "agg_table_write_policy" {
  name = "${local.resource_prefix}-agg-table-write-${local.environment}"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Action = [
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:BatchWriteItem",
        "dynamodb:GetItem",
        "dynamodb:Query",
        "dynamodb:Scan"
      ]
      Resource = [
        aws_dynamodb_table.catalogue_aggregation_table.arn,
        "${aws_dynamodb_table.catalogue_aggregation_table.arn}/index/*"
      ]
    }]
  })
}

/* 3️⃣  Attach the policies to the new role */
resource "aws_iam_role_policy_attachment" "bp_stream_read_attach" {
  role       = aws_iam_role.bolt_pricing_stream_role.name
  policy_arn = aws_iam_policy.bp_stream_read_policy.arn
}

resource "aws_iam_role_policy_attachment" "agg_table_write_attach" {
  role       = aws_iam_role.bolt_pricing_stream_role.name
  policy_arn = aws_iam_policy.agg_table_write_policy.arn
}

/* 4️⃣  Basic Lambda execution (logs, X-Ray, etc.) */
resource "aws_iam_role_policy_attachment" "bp_stream_basic_exec" {
  role       = aws_iam_role.bolt_pricing_stream_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "bp_stream_secrets" {
  role       = aws_iam_role.bolt_pricing_stream_role.name
  policy_arn = aws_iam_policy.secrets_manager_read.arn
}


################################################################################
# 7.  DLQ-metrics-poller Lambda
#     – reads DLQ URLs from SSM, then polls SQS + CloudWatch
################################################################################

# ── Execution role
resource "aws_iam_role" "dlq_metrics_poller_role" {
  name               = "${local.resource_prefix}-dlq-metrics-role-${local.environment}"
  assume_role_policy = local.lambda_assume_role_policy
}

# ── Custom least-privilege policy
resource "aws_iam_policy" "dlq_metrics_poller_policy" {
  name        = "${local.resource_prefix}-dlq-metrics-policy-${local.environment}"
  description = "SSM read, SQS attr read, CloudWatch metric read for DLQ metrics poller"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      # 1️⃣  Read DLQ URLs (String parameters) under /<prefix>/<env>/dlq/**
      {
        Sid    = "ReadDlqParameters"
        Effect = "Allow"
        Action = [
          "ssm:GetParametersByPath",
          "ssm:GetParameter",
          "ssm:GetParameters"
        ]
        Resource = "arn:aws:ssm:${var.aws_region}:${var.aws_accounts[local.deploy_env]}:parameter${local.dlq_ssm_root}*"
      },

      # 2️⃣  Poll depth / inflight counts
      {
        Sid    = "ReadQueueAttributes"
        Effect = "Allow"
        Action = "sqs:GetQueueAttributes"
        # Narrow to specific DLQ ARNs if you prefer; "*" is fine for read-only
        Resource = "*"
      },

      # 3️⃣  Pull CloudWatch age metric
      {
        Sid      = "ReadCwMetrics"
        Effect   = "Allow"
        Action   = "cloudwatch:GetMetricStatistics"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "dlq_metrics_poller_attach" {
  role       = aws_iam_role.dlq_metrics_poller_role.name
  policy_arn = aws_iam_policy.dlq_metrics_poller_policy.arn
}

resource "aws_iam_role_policy_attachment" "dlq_metrics_poller_basic_exec" {
  role       = aws_iam_role.dlq_metrics_poller_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}
