import {
  CreateTableCommand,
  DeleteTableCommand,
  DescribeTableCommand,
  DynamoDBClient,
  QueryCommand,
  type QueryCommandInput,
} from '@aws-sdk/client-dynamodb'
import { mockClient } from 'aws-sdk-client-mock'
import { unmarshall } from '@aws-sdk/util-dynamodb'
import {
  GetSecretValueCommand,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager'

import { DataDispatchService } from '../../../src/services/data-dispatch.service'
import { generateProductionStub } from '../../../src/__stubs__/production'
import { ProductionItem } from '../../../src/models'
import { createResourceName, getAWSConfig, waitFor } from '../setup/testSetup'

import type { Logger } from '@atg-digital/server-logger-library'
import type { ImportSummary } from '../../../src/clients/typesense'
import type { OutboxDbRecord } from '../../../src/clients/outbox'

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  child: jest.fn().mockReturnThis(),
  warn: jest.fn(),
  debug: jest.fn(),
} as unknown as Logger

const secretsManagerMock = mockClient(SecretsManagerClient)

describe('Event Outbox', () => {
  let dynamoClient: DynamoDBClient
  let tableName: string

  beforeAll(() => {
    const config = getAWSConfig()
    dynamoClient = new DynamoDBClient(config)

    secretsManagerMock.on(GetSecretValueCommand).resolves({
      SecretString: JSON.stringify({
        TYPESENSE_API_KEY: 'fake',
        TYPESENSE_CLUSTER: 'fake',
      }),
    })
  })

  beforeEach(async () => {
    tableName = createResourceName('test-table')
    process.env.OUTBOX_TABLE_NAME = tableName

    await dynamoClient.send(
      new CreateTableCommand({
        TableName: tableName,
        BillingMode: 'PAY_PER_REQUEST',
        KeySchema: [
          { AttributeName: 'pk', KeyType: 'HASH' },
          { AttributeName: 'sk', KeyType: 'RANGE' },
        ],
        AttributeDefinitions: [
          { AttributeName: 'pk', AttributeType: 'S' },
          { AttributeName: 'sk', AttributeType: 'S' },
          { AttributeName: 'GSI1PK', AttributeType: 'S' },
        ],
        GlobalSecondaryIndexes: [
          {
            IndexName: 'GSI1',
            KeySchema: [{ AttributeName: 'GSI1PK', KeyType: 'HASH' }],
            Projection: { ProjectionType: 'ALL' },
          },
        ],
        StreamSpecification: {
          StreamEnabled: true,
          StreamViewType: 'NEW_AND_OLD_IMAGES',
        },
      })
    )
  })

  afterEach(async () => {
    try {
      await dynamoClient.send(new DeleteTableCommand({ TableName: tableName }))

      await waitFor(
        async () => {
          try {
            await dynamoClient.send(
              new DescribeTableCommand({ TableName: tableName })
            )

            return false
          } catch (error) {
            return (
              (error as { name?: string })?.name === 'ResourceNotFoundException'
            )
          }
        },
        10000,
        500
      )
    } catch (error) {
      if ((error as { name?: string })?.name !== 'ResourceNotFoundException') {
        console.error('Error cleaning up resources:', error)
      }
    }
  })

  afterAll(() => {
    secretsManagerMock.reset()
  })

  it('should save outbox payload record as a buffer and GSI1PK as PENDING', async () => {
    const dispatch = await DataDispatchService.init(mockLogger)

    const production = new ProductionItem({ ...generateProductionStub() })

    const fakeImportSummary: ImportSummary = {
      tries: 1,
      attemptedCount: 1,
      success: { ids: [production.id], count: 1 },
      collection: 'show',
      op: 'imports',
    }

    jest
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .spyOn(dispatch as any, 'plan')
      .mockReturnValue([{ promise: Promise.resolve(fakeImportSummary) }])

    await dispatch.emit([{ alias: 'show', imports: [production] }])

    const params: QueryCommandInput = {
      TableName: tableName,
      KeyConditionExpression: 'pk = :pk',
      ExpressionAttributeValues: {
        ':pk': { S: `PRODUCTION#${production.sourceId}` },
      },
      Limit: 1,
      ReturnConsumedCapacity: 'TOTAL',
    }

    const { Items } = await dynamoClient.send(new QueryCommand(params))

    expect(Items?.length).toBe(1)

    const outboxRecord = unmarshall(Items![0]!) as OutboxDbRecord

    const payloadBuffer = Buffer.from(outboxRecord.payload as Uint8Array)

    expect(outboxRecord.pk).toBe(`PRODUCTION#${production.sourceId}`)
    expect(Buffer.isBuffer(payloadBuffer)).toBe(true)
    expect(outboxRecord.GSI1PK).toBe('PENDING')
  })
})
