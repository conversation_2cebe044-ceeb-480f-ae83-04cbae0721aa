import { execSync } from 'child_process'
import * as fs from 'fs'
import * as path from 'path'

import { GenericContainer, type StartedTestContainer } from 'testcontainers'

export default async function globalSetup(): Promise<void> {
  console.log('Starting LocalStack container for integration tests...')

  // Set required environment variables for Podman
  process.env.TESTCONTAINERS_DOCKER_SOCKET_OVERRIDE = '/var/run/docker.sock'

  // Build the Podman socket path dynamically using the current user's home directory
  const socketPath = execSync(
    'podman machine inspect --format "{{.ConnectionInfo.PodmanSocket.Path}}"',
    { encoding: 'utf-8' }
  ).trim()

  process.env.DOCKER_HOST = `unix://${socketPath}`
  process.env.TESTCONTAINERS_RYUK_DISABLED = 'true'

  try {
    // Start LocalStack using generic container to have more control
    const container = new GenericContainer('localstack/localstack:3.0.2')
      .withExposedPorts(4566)
      .withEnvironment({
        SERVICES: 'dynamodb,sqs,events,lambda,s3,stepfunctions,scheduler,logs',
        DEFAULT_REGION: 'eu-west-2',
        DEBUG: '0',
      })
      .withStartupTimeout(120000)

    const startedContainer: StartedTestContainer = await container.start()

    const containerInfo = {
      containerId: startedContainer.getId(),
      endpoint: `http://${startedContainer.getHost()}:${startedContainer.getMappedPort(4566)}`,
      host: startedContainer.getHost(),
      port: startedContainer.getMappedPort(4566),
      region: 'eu-west-2',
    }

    const tempDir = path.join(__dirname, '../.temp')
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }

    fs.writeFileSync(
      path.join(tempDir, 'container-info.json'),
      JSON.stringify(containerInfo, null, 2)
    )

    process.env.AWS_ENDPOINT_URL = containerInfo.endpoint
    process.env.AWS_REGION = 'eu-west-2'
    process.env.AWS_ACCESS_KEY_ID = 'test'
    process.env.AWS_SECRET_ACCESS_KEY = 'test'
    process.env.AWS_DEFAULT_REGION = 'eu-west-2'

    console.log('LocalStack container started successfully')
    console.log(`Endpoint: ${containerInfo.endpoint}`)
    console.log(`Container ID: ${containerInfo.containerId}`)

    // Store container reference for teardown
    // Use proper typing for global variable
    const globalWithContainer = global as typeof globalThis & {
      __TESTCONTAINERS_LOCALSTACK__: StartedTestContainer
    }
    globalWithContainer.__TESTCONTAINERS_LOCALSTACK__ = startedContainer
  } catch (error) {
    console.error('Failed to start LocalStack container:', error)
    throw error
  }
}
