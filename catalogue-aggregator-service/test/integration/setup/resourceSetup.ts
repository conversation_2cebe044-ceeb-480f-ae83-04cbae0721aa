import {
  CreateTableCommand,
  type DynamoDBClient,
} from '@aws-sdk/client-dynamodb'

import { generatePerformanceStub } from '../../../src/__stubs__/mock-performance'
import { generateProductionStub } from '../../../src/__stubs__/production'
import { ProductionItem, TourItem } from '../../../src/models'
import { generateContentfulTourStub } from '../../../src/__stubs__/tour'

export function createLinkedItems({ numProductions = 1 }) {
  const showSlug = 'fake-linked-show-slug'
  const venueSlug = 'fake-linked-venue-slug'
  const tourId = 'fake-linked-tour-id'
  const tourName = 'fake-linked-tour-name'

  const performance = generatePerformanceStub({
    titleSlug: showSlug,
    venueSlug,
  })

  const productions = Array.from({ length: numProductions }, (_, i) => {
    const productionStub = generateProductionStub()
    const suffix = i > 0 ? `-${i}` : ''

    return new ProductionItem({
      ...productionStub,
      _id: productionStub._id + suffix,
      sourceId: productionStub.sourceId + suffix,
      thirdPartyEvent: false,
      showSlug,
      venue: {
        ...productionStub.venue,
        config: {
          ...productionStub.venue.config,
          venueConfigSlug: venueSlug + suffix,
        },
      },
      tourIds: [tourId],
      tourNames: [tourName],
      performancesIds: [performance.id],
    } as ProductionItem)
  })

  const tour = new TourItem({
    ...generateContentfulTourStub({ _id: tourId, name: tourName }),
    sourceType: 'contentful',
    showSlug,
  })

  return {
    performance,
    productions,
    tour,
  }
}

export async function createDynamoResource(
  dynamoClient: DynamoDBClient,
  tableName: string
) {
  await dynamoClient.send(
    new CreateTableCommand({
      TableName: tableName,
      BillingMode: 'PAY_PER_REQUEST',
      StreamSpecification: {
        StreamEnabled: true,
        StreamViewType: 'NEW_AND_OLD_IMAGES',
      },
      KeySchema: [{ AttributeName: 'pk', KeyType: 'HASH' }],
      AttributeDefinitions: [
        { AttributeName: 'pk', AttributeType: 'S' },
        { AttributeName: 'GSI1PK', AttributeType: 'S' },
        { AttributeName: 'GSI1SK', AttributeType: 'S' },
      ],
      GlobalSecondaryIndexes: [
        {
          IndexName: 'GSI1',
          KeySchema: [
            { AttributeName: 'GSI1PK', KeyType: 'HASH' },
            { AttributeName: 'GSI1SK', KeyType: 'RANGE' },
          ],
          Projection: {
            ProjectionType: 'ALL',
          },
        },
      ],
    })
  )
}
