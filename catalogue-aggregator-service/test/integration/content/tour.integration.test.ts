/* eslint-disable @typescript-eslint/no-empty-function */

import {
  DeleteTableCommand,
  DescribeTableCommand,
  DynamoDBClient,
  GetItemCommand,
  PutItemCommand,
  ScanCommand,
} from '@aws-sdk/client-dynamodb'
import { mockClient } from 'aws-sdk-client-mock'
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from '@aws-sdk/client-secrets-manager'
import { marshall, unmarshall } from '@aws-sdk/util-dynamodb'

import { createResourceName, getAWSConfig, waitFor } from '../setup/testSetup'
import { createDynamoResource, createLinkedItems } from '../setup/resourceSetup'
import {
  generateContentfulTourStub,
  generateUmbracoTourStub,
} from '../../../src/__stubs__/tour'
import { handler } from '../../../src/handlers/content/tour/upsert'
import { ProductionItem, TourItem } from '../../../src/models'
import * as loggerUtils from '../../../src/utils/logger'
import { Status } from '../../../src/interfaces'
import { now } from '../../../src/utils/dates'
import { pick } from '../../../src/utils/common'
import { handler as aggregate } from '../../../src/handlers/inventory/aggregate'

import type { Aggregatable } from '../../../src/services/aggregate.service'
import type { Tour } from '@atg-digital/cms-types'
import type { Context, EventBridgeEvent, SQSRecord } from 'aws-lambda'

const PK_PREFIX = 'TOUR'

const secretsManagerMock = mockClient(SecretsManagerClient)

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  child: jest.fn().mockReturnThis(),
}

jest.mock('../../../src/utils/logger')
jest.mock('@slack/webhook', () => {
  return {
    IncomingWebhook: jest.fn().mockImplementation(() => ({
      send: jest.fn().mockResolvedValue({ ok: true }),
    })),
  }
})

function makeAggregateSqsEvent({
  venueSlug,
  showSlug,
  tenantCode,
}: {
  venueSlug: string
  showSlug: string
  tenantCode: string
}): SQSRecord {
  const key = `${venueSlug}#${showSlug}#${tenantCode}`

  const dedupId = `${key}-${Math.floor(Date.now() / 10_000)}`

  return {
    messageId: dedupId,
    receiptHandle: 'mock-receipt-handle',
    body: JSON.stringify({
      showSlug,
      venueSlug,
      tenantCode,
    }),
    attributes: {
      ApproximateReceiveCount: '1',
      SentTimestamp: Date.now().toString(),
      SequenceNumber: '1',
      SenderId: '************',
      ApproximateFirstReceiveTimestamp: Date.now().toString(),
    },
    messageAttributes: {
      traceId: {
        stringValue: 'mock-trace-id',
        dataType: 'String',
      },
      entity: {
        stringValue: 'QUEUE_PERF_AGG',
        dataType: 'String',
      },
    },
    md5OfBody: 'mock-md5',
    eventSource: 'aws:sqs',
    eventSourceARN: 'arn:aws:sqs:us-east-1:************:my-queue',
    awsRegion: 'eu-west-2',
  } as SQSRecord
}

const makeEvent = (
  detail: Tour,
  source: 'umbraco' | 'contentful'
): EventBridgeEvent<string, Tour> => ({
  'version': '0',
  'id': 'evt-1',
  'detail-type': 'publish',
  'source': `atg.cms-listener.${source}.tour`,
  'account': 'acc',
  'time': new Date().toISOString(),
  'region': 'eu-west-2',
  'resources': [],
  detail,
})

const ctx = {
  awsRequestId: 'req-1',
  getRemainingTimeInMillis: () => 3000,
} as Context

describe('Tour Data Ingestion', () => {
  let dynamoClient: DynamoDBClient
  let tableName: string

  beforeAll(() => {
    const config = getAWSConfig()
    dynamoClient = new DynamoDBClient(config)

    secretsManagerMock.on(GetSecretValueCommand).resolves({
      SecretString: JSON.stringify({
        TYPESENSE_API_KEY: 'fake',
        TYPESENSE_CLUSTER: 'fake',
      }),
    })
  })

  beforeEach(async () => {
    tableName = createResourceName('test-table')
    process.env.TABLE_NAME = tableName

    await createDynamoResource(dynamoClient, tableName)
    ;(loggerUtils.loggerForEventBus as jest.Mock).mockReturnValue(mockLogger)
    ;(loggerUtils.loggerForSQS as jest.Mock).mockReturnValue(mockLogger)
  })

  afterEach(async () => {
    try {
      await dynamoClient.send(new DeleteTableCommand({ TableName: tableName }))

      await waitFor(
        async () => {
          try {
            await dynamoClient.send(
              new DescribeTableCommand({ TableName: tableName })
            )

            return false
          } catch (error) {
            return (
              (error as { name?: string })?.name === 'ResourceNotFoundException'
            )
          }
        },
        10000,
        500
      )
    } catch (error) {
      if ((error as { name?: string })?.name !== 'ResourceNotFoundException') {
        console.error('Error cleaning up resources:', error)
      }
    }
  })

  afterAll(() => {
    secretsManagerMock.reset()
  })

  it('should create record and map websiteSlugs correctly given an event from atg.cms-listener.umbraco.tour source', async () => {
    const tourStub = generateUmbracoTourStub()

    const mockEvent = makeEvent(tourStub, 'umbraco')

    await handler(mockEvent, ctx, () => {})

    const { Item } = await dynamoClient.send(
      new GetItemCommand({
        TableName: tableName,
        Key: marshall({
          pk: `${PK_PREFIX}#${tourStub._id}`,
        }),
      })
    )

    expect(Item).toBeDefined()

    const record = unmarshall(Item!) as TourItem

    expect(record._id).toBe(tourStub._id)
    expect(record.name).toBe(tourStub.name)
    expect(record.contentTypeAlias).toBe('tour')
    expect(record.locale).toBe(tourStub.locale)
    expect(record.sourceType).toBe('umbraco')
    expect(record.GSI1PK).toBe(`${PK_PREFIX}#${tourStub.brandAlias}`)
    expect(record.websiteSlug).toBe(tourStub.websiteSlug)
  })

  it('should update existing record in the db given an event from atg.cms-listener.umbraco.tour source', async () => {
    const tourStub = generateUmbracoTourStub()

    const tourItem = new TourItem({
      ...tourStub,
      sourceType: 'umbraco',
    })

    await dynamoClient.send(
      new PutItemCommand({
        TableName: tableName,
        Item: tourItem.toDbItem(),
      })
    )

    const updatedTourName = 'Fake Updated Tour Name'

    const mockEvent = makeEvent(
      { ...tourStub, name: updatedTourName },
      'umbraco'
    )

    await handler(mockEvent, ctx, () => {})

    const { Item } = await dynamoClient.send(
      new GetItemCommand({
        TableName: tableName,
        Key: marshall({
          pk: `${PK_PREFIX}#${tourStub._id}`,
        }),
      })
    )

    expect(Item).toBeDefined()

    const record = unmarshall(Item!) as TourItem

    expect(record._id).toBe(tourStub._id)
    expect(record.name).toBe(updatedTourName)
    expect(record.GSI1PK).toBe(`${PK_PREFIX}#${tourStub.brandAlias}`)
    expect(record.GSI1SK).toBe(tourStub.websiteSlug)
  })

  it('should create record and map websiteSlugs correctly given an event from atg.cms-listener.contentful.tour source', async () => {
    const tourStub = generateContentfulTourStub()

    const mockEvent = makeEvent(tourStub, 'contentful')

    await handler(mockEvent, ctx, () => {})

    const { Item } = await dynamoClient.send(
      new GetItemCommand({
        TableName: tableName,
        Key: marshall({
          pk: `${PK_PREFIX}#${tourStub._id}`,
        }),
      })
    )

    expect(Item).toBeDefined()

    const record = unmarshall(Item!) as TourItem

    expect(record._id).toBe(tourStub._id)
    expect(record.name).toBe(tourStub.name)
    expect(record.contentTypeAlias).toBe('tour')
    expect(record.locale).toBe(tourStub.locale)
    expect(record.sourceType).toBe('contentful')
    expect(record.GSI1PK).toBe(`${PK_PREFIX}#${tourStub.brandAlias}`)
    expect(record.websiteSlug).toBe(tourStub.websiteSlug)
  })

  it('should create a record in the db given an event from atg.cms-listener.contentful.tour source and same name as an Umbraco tour', async () => {
    const contentfulTourStub = generateContentfulTourStub()

    const umbracoTourStub = generateUmbracoTourStub()

    const tourItem = new TourItem({
      ...umbracoTourStub,
      sourceType: 'umbraco',
    })

    await dynamoClient.send(
      new PutItemCommand({
        TableName: tableName,
        Item: tourItem.toDbItem(),
      })
    )

    const mockEvent = makeEvent(contentfulTourStub, 'contentful')

    await handler(mockEvent, ctx, () => {})

    const { Items } = await dynamoClient.send(
      new ScanCommand({
        TableName: tableName,
        FilterExpression: 'begins_with(#pk, :prefix)',
        ExpressionAttributeNames: {
          '#pk': 'pk',
        },
        ExpressionAttributeValues: {
          ':prefix': { S: 'TOUR' },
        },
      })
    )

    const contentfulRecord = Items?.map(TourItem.fromDbItem).find(
      (item) => item.sourceType === 'contentful'
    )

    expect(Items).toHaveLength(2)
    expect(contentfulRecord?._id).toBe(contentfulTourStub._id)
    expect(contentfulRecord?.name).toBe(contentfulTourStub.name)
    expect(contentfulRecord?.contentTypeAlias).toBe('tour')
    expect(contentfulRecord?.locale).toBe(contentfulTourStub.locale)
    expect(contentfulRecord?.sourceType).toBe('contentful')
    expect(contentfulRecord?.GSI1PK).toBe(
      `${PK_PREFIX}#${contentfulTourStub.brandAlias}`
    )
    expect(contentfulRecord?.GSI1SK).toBe(contentfulTourStub.websiteSlug)
  })

  describe('Tour Status Updates', () => {
    process.env.LOG_LEVEL = 'info'
    process.env.ENABLE_DEBUG = 'false'
    process.env.SLACK_ALERTS_WEBHOOK = ''

    const onsaleDateAssign: Partial<Aggregatable> = {
      embargoEnds: now().subtract(3, 'days').toISOString(),
      onSaleStartDate: now().subtract(1, 'day').toISOString(),
      startDate: now().add(5, 'days').toISOString(),
      preSaleStarts: '',
    }

    async function fetchEntities(pks: string[]) {
      const getItem = async (pk: string) =>
        dynamoClient.send(
          new GetItemCommand({
            TableName: tableName,
            Key: marshall({ pk }),
          })
        )

      const results = await Promise.all(pks.map(getItem))

      return results.reduce<{
        productions: ProductionItem[]
        tours: TourItem[]
      }>(
        (acc, res, i) => {
          const pk = pks[i]

          if (pk!.startsWith('PRODUCTION#')) {
            acc.productions.push(ProductionItem.fromDbItem(res.Item!))
          }

          if (pk!.startsWith('TOUR#')) {
            acc.tours.push(TourItem.fromDbItem(res.Item!))
          }

          return acc
        },
        { productions: [], tours: [] }
      )
    }

    it('should update the tour status to be onsale when embargo ends', async () => {
      const { performance, productions, tour } = createLinkedItems({})

      for (const item of [productions[0]!, tour]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: {
              ...item.toDbItem(),
              ...marshall({
                status: Status.EMBARGO,
                ...onsaleDateAssign,
              }),
            },
          })
        )
      }

      const pre = await fetchEntities([productions[0]!.pk, tour.pk])

      expect(pre.tours[0]!.status).toBe(Status.EMBARGO)
      expect(pre.productions[0]!.status).toBe(Status.EMBARGO)

      await dynamoClient.send(
        new PutItemCommand({
          TableName: tableName,
          Item: {
            ...performance.toDbItem(),
            ...marshall({
              ...onsaleDateAssign,
              status: Status.GENERAL_ON_SALE,
            }),
          },
        })
      )

      await aggregate(
        {
          Records: [
            makeAggregateSqsEvent({
              ...pick(performance, ['showSlug', 'venueSlug', 'tenantCode']),
            }),
          ],
        },
        ctx,
        () => {}
      )
      const update = await fetchEntities([productions[0]!.pk, tour.pk])

      expect(update.tours[0]!.status).toBe(Status.GENERAL_ON_SALE)
      expect(update.productions[0]!.status).toBe(Status.GENERAL_ON_SALE)
    })

    it('should update the tour status to be pre-sale when embargo ends', async () => {
      const { performance, productions, tour } = createLinkedItems({})

      for (const item of [productions[0]!, tour]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: {
              ...item.toDbItem(),
              ...marshall({
                status: Status.EMBARGO,
                ...onsaleDateAssign,
              }),
            },
          })
        )
      }

      const pre = await fetchEntities([productions[0]!.pk, tour.pk])

      expect(pre.tours[0]!.status).toBe(Status.EMBARGO)
      expect(pre.productions[0]!.status).toBe(Status.EMBARGO)

      await dynamoClient.send(
        new PutItemCommand({
          TableName: tableName,
          Item: {
            ...performance.toDbItem(),
            ...marshall({
              ...onsaleDateAssign,
              preSaleStarts: now().subtract(1, 'day').toISOString(),
              onSaleStartDate: now().add(10, 'hour').toISOString(),
              status: Status.PRE_SALE,
            }),
          },
        })
      )

      await aggregate(
        {
          Records: [
            makeAggregateSqsEvent({
              ...pick(performance, ['showSlug', 'venueSlug', 'tenantCode']),
            }),
          ],
        },
        ctx,

        () => {}
      )
      const update = await fetchEntities([productions[0]!.pk, tour.pk])

      expect(update.tours[0]!.status).toBe(Status.PRE_SALE)
      expect(update.productions[0]!.status).toBe(Status.PRE_SALE)
    })

    it('should update the tour status to be sold out when last production sells out', async () => {
      const { performance, productions, tour } = createLinkedItems({
        numProductions: 3,
      })

      const onsaleProduction = Object.assign(productions[0]!, {
        status: Status.GENERAL_ON_SALE,
      })

      const soldoutProductionOne = new ProductionItem({
        ...productions[1],
        status: Status.SOLD_OUT,
      } as ProductionItem)

      const soldoutProductionTwo = new ProductionItem({
        ...productions[2],
        status: Status.SOLD_OUT,
      } as ProductionItem)

      const onsaleTour = Object.assign(tour, {
        status: Status.GENERAL_ON_SALE,
      })

      for (const item of [
        onsaleProduction,
        soldoutProductionOne,
        soldoutProductionTwo,
        onsaleTour,
      ]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: item.toDbItem(),
          })
        )
      }

      const pre = await fetchEntities([
        onsaleProduction.pk,
        soldoutProductionOne.pk,
        soldoutProductionTwo.pk,
        onsaleTour.pk,
      ])

      const preSoldoutProdOne = pre.productions.find(
        (p) => p._id === soldoutProductionOne._id
      )
      const preSoldoutProdTwo = pre.productions.find(
        (p) => p._id === soldoutProductionTwo._id
      )
      const preOnsaleProd = pre.productions.find(
        (p) => p._id === onsaleProduction._id
      )
      const preOnsaleTour = pre.tours.find((t) => t._id === onsaleTour._id)

      expect(preSoldoutProdOne?.status).toBe(Status.SOLD_OUT)
      expect(preSoldoutProdTwo?.status).toBe(Status.SOLD_OUT)
      expect(preOnsaleProd?.status).toBe(Status.GENERAL_ON_SALE)
      expect(preOnsaleTour?.status).toBe(Status.GENERAL_ON_SALE)

      Object.assign(performance, {
        status: Status.SOLD_OUT,
        isSoldOut: true,
        seatsAvailable: 0,
        startDate: now().add(10, 'days').toISOString(),
      })

      await dynamoClient.send(
        new PutItemCommand({
          TableName: tableName,
          Item: performance.toDbItem(),
        })
      )
      await aggregate(
        {
          Records: [
            makeAggregateSqsEvent({
              ...pick(performance, ['showSlug', 'venueSlug', 'tenantCode']),
            }),
          ],
        },
        ctx,

        () => {}
      )

      const update = await fetchEntities([onsaleProduction.pk, onsaleTour.pk])

      const updateProduction = update.productions.find(
        (p) => p._id === onsaleProduction._id
      )
      const updateTour = update.tours.find((t) => t._id === onsaleTour._id)

      expect(updateProduction?.status).toBe(Status.SOLD_OUT)
      expect(updateTour?.status).toBe(Status.SOLD_OUT)
    })

    it('should make a tour visible again if a new production is added', async () => {
      const { performance, productions, tour } = createLinkedItems({
        numProductions: 3,
      })

      const onsaleProduction = Object.assign(productions[0]!, {
        status: Status.GENERAL_ON_SALE,
      })

      const soldoutProductionOne = new ProductionItem({
        ...productions[1],
        status: Status.SOLD_OUT,
      } as ProductionItem)

      const soldoutProductionTwo = new ProductionItem({
        ...productions[2],
        status: Status.SOLD_OUT,
      } as ProductionItem)

      const soldoutTour = Object.assign(tour, {
        status: Status.SOLD_OUT,
      })

      for (const item of [
        soldoutProductionOne,
        soldoutProductionTwo,
        soldoutTour,
      ]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: item.toDbItem(),
          })
        )
      }

      const pre = await fetchEntities([
        soldoutProductionOne.pk,
        soldoutProductionTwo.pk,
        soldoutTour.pk,
      ])

      const preSoldoutProdOne = pre.productions.find(
        (p) => p._id === soldoutProductionOne._id
      )
      const preSoldoutProdTwo = pre.productions.find(
        (p) => p._id === soldoutProductionTwo._id
      )
      const preSoldoutTour = pre.tours.find((t) => t._id === soldoutTour._id)

      expect(preSoldoutProdOne?.status).toBe(Status.SOLD_OUT)
      expect(preSoldoutProdTwo?.status).toBe(Status.SOLD_OUT)
      expect(preSoldoutTour?.status).toBe(Status.SOLD_OUT)

      const dateWithStatus = {
        ...onsaleDateAssign,
        status: Status.GENERAL_ON_SALE,
      }

      Object.assign(performance, dateWithStatus)
      Object.assign(onsaleProduction, dateWithStatus)

      for (const item of [performance, onsaleProduction]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: item.toDbItem(),
          })
        )
      }

      await aggregate(
        {
          Records: [
            makeAggregateSqsEvent({
              ...pick(performance, ['showSlug', 'venueSlug', 'tenantCode']),
            }),
          ],
        },
        ctx,
        () => {}
      )

      const update = await fetchEntities([soldoutTour.pk])

      expect(update.tours[0]?.status).toBe(Status.GENERAL_ON_SALE)
    })

    it('should update to closed status if all productions are closed', async () => {
      const { performance, productions, tour } = createLinkedItems({
        numProductions: 3,
      })

      const onsaleProduction = Object.assign(productions[0]!, {
        status: Status.GENERAL_ON_SALE,
      })

      const closedProductionOne = new ProductionItem({
        ...productions[1],
        status: Status.CLOSED,
      } as ProductionItem)

      const closedProductionTwo = new ProductionItem({
        ...productions[2],
        status: Status.CLOSED,
      } as ProductionItem)

      const onsaleTour = Object.assign(tour, {
        status: Status.GENERAL_ON_SALE,
      })

      for (const item of [
        onsaleProduction,
        closedProductionOne,
        closedProductionTwo,
        onsaleTour,
      ]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: item.toDbItem(),
          })
        )
      }

      const pre = await fetchEntities([
        onsaleProduction.pk,
        closedProductionOne.pk,
        closedProductionTwo.pk,
        onsaleTour.pk,
      ])

      const preClosedProdOne = pre.productions.find(
        (p) => p._id === closedProductionOne._id
      )
      const preClosedProdTwo = pre.productions.find(
        (p) => p._id === closedProductionTwo._id
      )
      const preOnsaleProd = pre.productions.find(
        (p) => p._id === onsaleProduction._id
      )
      const preOnsaleTour = pre.tours.find((t) => t._id === onsaleTour._id)

      expect(preClosedProdOne?.status).toBe(Status.CLOSED)
      expect(preClosedProdTwo?.status).toBe(Status.CLOSED)
      expect(preOnsaleProd?.status).toBe(Status.GENERAL_ON_SALE)
      expect(preOnsaleTour?.status).toBe(Status.GENERAL_ON_SALE)

      Object.assign(performance, { status: Status.CLOSED })

      await dynamoClient.send(
        new PutItemCommand({
          TableName: tableName,
          Item: performance.toDbItem(),
        })
      )

      await aggregate(
        {
          Records: [
            makeAggregateSqsEvent({
              ...pick(performance, ['showSlug', 'venueSlug', 'tenantCode']),
            }),
          ],
        },
        ctx,
        () => {}
      )

      const update = await fetchEntities([onsaleTour.pk, onsaleProduction.pk])

      expect(update.productions[0]?.status).toBe(Status.CLOSED)
      expect(update.tours[0]?.status).toBe(Status.CLOSED)
    })

    it('should update all tours correctly to reflect a given production change', async () => {
      const { performance, productions, tour } = createLinkedItems({
        numProductions: 2,
      })

      Object.assign(tour, { status: Status.COMING_SOON })

      const otherTour = new TourItem({
        ...tour,
        _id: 'other-fake-tour-id',
        name: 'other-fake-tour-name',
        showSlug: 'other-fake-show-slug',
        status: Status.COMING_SOON,
      })

      const comingSoonProduction = Object.assign(productions[0]!, {
        status: Status.COMING_SOON,
        tourIds: [tour._id, otherTour._id],
        tourNames: [tour.name, otherTour.name],
      })

      const closedProduction = new ProductionItem({
        ...productions[1],
        status: Status.CLOSED,
      } as ProductionItem)

      for (const item of [
        tour,
        otherTour,
        comingSoonProduction,
        closedProduction,
      ]) {
        await dynamoClient.send(
          new PutItemCommand({
            TableName: tableName,
            Item: item.toDbItem(),
          })
        )
      }

      const pre = await fetchEntities([
        tour.pk,
        otherTour.pk,
        comingSoonProduction.pk,
        closedProduction.pk,
      ])

      const preComingSoonProd = pre.productions.find(
        (p) => p._id === comingSoonProduction._id
      )
      const preClosedProd = pre.productions.find(
        (p) => p._id === closedProduction._id
      )
      const preTour = pre.tours.find((t) => t._id === tour._id)

      const preOtherTour = pre.tours.find((t) => t._id === otherTour._id)

      expect(preComingSoonProd?.status).toBe(Status.COMING_SOON)
      expect(preClosedProd?.status).toBe(Status.CLOSED)
      expect(preOtherTour?.status).toBe(Status.COMING_SOON)
      expect(preTour?.status).toBe(Status.COMING_SOON)

      Object.assign(performance, {
        status: Status.GENERAL_ON_SALE,
        ...onsaleDateAssign,
      })

      await dynamoClient.send(
        new PutItemCommand({
          TableName: tableName,
          Item: performance.toDbItem(),
        })
      )

      await aggregate(
        {
          Records: [
            makeAggregateSqsEvent({
              ...pick(performance, ['showSlug', 'venueSlug', 'tenantCode']),
            }),
          ],
        },
        ctx,
        () => {}
      )

      const update = await fetchEntities([
        comingSoonProduction.pk,
        otherTour.pk,
        tour.pk,
      ])

      expect(update.tours[0]?.status).toBe(Status.GENERAL_ON_SALE)
      expect(update.tours[1]?.status).toBe(Status.GENERAL_ON_SALE)
      expect(update.productions[0]?.status).toBe(Status.GENERAL_ON_SALE)
    })
  })
})
