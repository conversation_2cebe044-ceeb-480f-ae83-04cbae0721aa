export const eventFunctions = {
  /*───────────────────────────────────────────────────────────────────*\
    1. Outbox → Event<PERSON>ridge streamer
  \*───────────────────────────────────────────────────────────────────*/
  emitOutboxEvents: {
    name: '${self:custom.resourcePrefix}-emit-outbox-events--${self:custom.stage}',
    handler: 'src/handlers/event/emit-outbox.handler',
    role: '${self:custom.ssmParams.streamListenerRoleArn}',
    timeout: 15,
    events: [
      {
        stream: {
          type: 'dynamodb',
          arn: '${self:custom.ssmParams.eventOutboxTableStreamArn}',
          filterPatterns: [{ eventName: ['INSERT'] }],
          startingPosition: 'LATEST',
          batchSize: 100,
          batchWindow: 5,
          maximumRetryAttempts: 1,
        },
      },
      {
        schedule: {
          name: 'getUnprocessedEvents',
          rate: ['rate(4 hours)'],
          input: {},
        },
      },
    ],
    description:
      'Streams and polls for outbox events and raises them to the Catalogue event bus',
  },

  /*───────────────────────────────────────────────────────────────────*\
    2. DLQ-metrics poller
       – runs every 5 minutes, pulls depth / inflight / age metrics
  \*───────────────────────────────────────────────────────────────────*/
  dlqMetricPoller: {
    name: '${self:custom.resourcePrefix}-dlq-metric-poller--${self:custom.stage}',
    handler: 'src/handlers/event/dlq-metrics-poller.handler',
    role: '${self:custom.ssmParams.dlqMetricsPollerRoleArn}',
    memorySize: 128,
    timeout: 30,
    events: [
      {
        schedule: {
          name: 'pollDlqMetrics',
          rate: ['rate(5 minutes)'],
          enabled: true,
          input: {},
        },
      },
    ],
    description:
      'Polls DLQ depth, inflight count and oldest-message age, emits custom metrics for NR dashboards',
  },
}
